from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional
from ..base.bmad_agent import BMadAgent

class TechnicalComponent(BaseModel):
    name: str
    description: str
    technology: str
    responsibilities: List[str]
    interfaces: List[str] = Field(default_factory=list)
    dependencies: List[str] = Field(default_factory=list)

class ArchitecturalDecision(BaseModel):
    decision: str
    rationale: str
    alternatives_considered: List[str]
    implications: List[str]
    risks: List[str] = Field(default_factory=list)

class SystemArchitecture(BaseModel):
    overview: str
    components: List[TechnicalComponent]
    architectural_decisions: List[ArchitecturalDecision]
    technology_stack: Dict[str, str]
    deployment_considerations: List[str]
    scalability_considerations: List[str]
    security_considerations: List[str]

class ArchitectureReview(BaseModel):
    strengths: List[str]
    improvements: List[str]
    recommendations: List[str]
    risk_assessment: List[str]
    overall_score: int = Field(ge=1, le=10)

class TechnicalSpecification(BaseModel):
    component_name: str
    detailed_description: str
    api_specifications: List[str]
    data_models: List[str]
    integration_points: List[str]
    performance_requirements: List[str]
    testing_considerations: List[str]

class ArchitectAgent(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are an expert Software Architect in the BMad Method framework.
        
        Your responsibilities include:
        - Designing system architecture and technical solutions
        - Making technology stack decisions
        - Defining component interfaces and interactions
        - Considering scalability, security, and maintainability
        - Documenting architectural decisions and rationale
        
        Always provide well-reasoned architectural decisions with clear rationale.
        Consider both current requirements and future scalability needs.
        Focus on maintainable, testable, and robust solutions.
        """
        
        super().__init__(
            role="architect",
            system_prompt=system_prompt,
            response_model=SystemArchitecture
        )
    
    async def design_architecture(self, requirements: str, constraints: Dict[str, Any] = None) -> SystemArchitecture:
        """Design system architecture based on requirements."""
        prompt = f"""
        Design a comprehensive system architecture for the following requirements:
        
        Requirements:
        {requirements}
        
        {f"Constraints: {constraints}" if constraints else ""}
        
        Provide a detailed architecture including:
        - System overview and high-level design
        - Key components with responsibilities and interfaces
        - Architectural decisions with rationale
        - Technology stack recommendations
        - Deployment, scalability, and security considerations
        """
        
        result = await self.run(prompt)
        return result
    
    async def review_architecture(self, architecture: SystemArchitecture, criteria: List[str]) -> ArchitectureReview:
        """Review existing architecture against specific criteria."""
        prompt = f"""
        Review the following system architecture against these criteria:
        
        Architecture Overview: {architecture.overview}
        Components: {len(architecture.components)} components defined
        Technology Stack: {architecture.technology_stack}
        
        Review Criteria:
        {', '.join(criteria)}
        
        Provide a detailed review with:
        - Strengths of the current architecture
        - Areas for improvement
        - Specific recommendations
        - Risk assessment
        - Overall score (1-10)
        """
        
        temp_agent = BMadAgent(
            role="architect-review",
            system_prompt=self.system_prompt,
            response_model=ArchitectureReview
        )
        
        return await temp_agent.run(prompt)
    
    async def create_technical_specs(self, component: TechnicalComponent, requirements: str) -> TechnicalSpecification:
        """Create detailed technical specifications for a component."""
        prompt = f"""
        Create detailed technical specifications for the following component:
        
        Component: {component.name}
        Description: {component.description}
        Technology: {component.technology}
        Responsibilities: {', '.join(component.responsibilities)}
        
        Requirements Context:
        {requirements}
        
        Provide comprehensive technical specifications including:
        - Detailed component description
        - API specifications and endpoints
        - Data models and schemas
        - Integration points with other components
        - Performance requirements and benchmarks
        - Testing considerations and strategies
        """
        
        temp_agent = BMadAgent(
            role="architect-specs",
            system_prompt=self.system_prompt,
            response_model=TechnicalSpecification
        )
        
        return await temp_agent.run(prompt)
    
    async def validate_technology_stack(self, stack: Dict[str, str], requirements: List[str]) -> Dict[str, Any]:
        """Validate technology stack choices against requirements."""
        prompt = f"""
        Validate the following technology stack against the given requirements:
        
        Technology Stack:
        {stack}
        
        Requirements:
        {', '.join(requirements)}
        
        Provide validation results including:
        - Compatibility assessment
        - Performance implications
        - Scalability considerations
        - Maintenance and support considerations
        - Alternative recommendations if needed
        """
        
        class StackValidation(BaseModel):
            compatibility_score: int = Field(ge=1, le=10)
            performance_assessment: str
            scalability_rating: int = Field(ge=1, le=10)
            maintenance_complexity: str
            recommendations: List[str]
            alternatives: List[str] = Field(default_factory=list)
        
        temp_agent = BMadAgent(
            role="architect-validation",
            system_prompt=self.system_prompt,
            response_model=StackValidation
        )
        
        result = await temp_agent.run(prompt)
        return result.model_dump()