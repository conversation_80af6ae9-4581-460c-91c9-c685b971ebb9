#!/usr/bin/env python3
"""
Test script for BMad base infrastructure.
Verifies that the base components work correctly with Pydantic AI.
"""

import asyncio
import os
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import BMad components
from bmad_agents import (
    BMadAgent,
    AgentMessage,
    AgentRequest,
    AgentResponse,
    StateManager,
    BMadConfig,
    MessageType,
    Priority,
    initialize_bmad_system
)

async def test_base_infrastructure():
    """Test the base BMad infrastructure components."""
    print("🚀 Testing BMad Base Infrastructure")
    print("=" * 50)
    
    # Initialize the system
    logger_system = initialize_bmad_system(log_level="INFO")
    print("✅ BMad system initialized")
    
    # Test configuration
    print("\n📋 Testing Configuration...")
    config = BMadConfig()
    available_agents = config.list_available_agents()
    available_workflows = config.list_available_workflows()
    
    print(f"Available agents: {available_agents}")
    print(f"Available workflows: {available_workflows}")
    print(f"Default model: {config.get_global_config('default_model')}")
    
    # Validate configuration
    issues = config.validate_configuration()
    if issues['errors']:
        print(f"❌ Configuration errors: {issues['errors']}")
    else:
        print("✅ Configuration validation passed")
    
    # Test state manager
    print("\n💾 Testing State Manager...")
    state_manager = StateManager()
    
    # Create a test workflow state
    from bmad_agents.base.communication import WorkflowState
    test_workflow = WorkflowState(
        workflow_id="test-workflow-001",
        workflow_type="test",
        current_step="initialization",
        status="active",
        progress=0.1,
        started_at=datetime.now(),
        context={"test": "data"},
        participants=["analyst", "architect"],
        completed_steps=[],
        pending_steps=["analysis", "design"]
    )
    
    # Save and load workflow state
    save_success = state_manager.save_workflow_state(test_workflow)
    if save_success:
        print("✅ Workflow state saved successfully")
        
        loaded_workflow = state_manager.load_workflow_state("test-workflow-001")
        if loaded_workflow and loaded_workflow.workflow_id == test_workflow.workflow_id:
            print("✅ Workflow state loaded successfully")
        else:
            print("❌ Failed to load workflow state")
    else:
        print("❌ Failed to save workflow state")
    
    # Test agent message creation
    print("\n💬 Testing Agent Communication...")
    test_message = AgentMessage(
        id="msg-001",
        type=MessageType.REQUEST,
        from_agent="test-system",
        to_agent="analyst",
        priority=Priority.NORMAL,
        content={"task": "analyze requirements", "data": "test data"},
        workflow_id="test-workflow-001"
    )
    
    # Save message
    message_saved = state_manager.save_message(test_message)
    if message_saved:
        print("✅ Agent message saved successfully")
    else:
        print("❌ Failed to save agent message")
    
    # Test basic agent creation (without API call)
    print("\n🤖 Testing BMad Agent Creation...")
    try:
        # Create a test agent
        test_agent = BMadAgent(
            role="test-agent",
            model="gemini-2.5-flash",
            system_prompt="You are a test agent for the BMad system."
        )
        
        agent_info = test_agent.get_agent_info()
        print(f"✅ Test agent created: {agent_info['role']}")
        print(f"   Model: {agent_info['model']}")
        print(f"   Capabilities: {agent_info['capabilities']}")
        
    except Exception as e:
        print(f"❌ Failed to create test agent: {str(e)}")
    
    # Test API key availability (without making actual calls)
    print("\n🔑 Testing API Configuration...")
    google_key = config.get_api_key('google_ai')
    openai_key = config.get_api_key('openai')
    
    if google_key:
        print(f"✅ Google AI API key configured (length: {len(google_key)})")
    else:
        print("⚠️  Google AI API key not found")
    
    if openai_key:
        print(f"✅ OpenAI API key configured (length: {len(openai_key)})")
    else:
        print("⚠️  OpenAI API key not found")
    
    # Clean up test data
    print("\n🧹 Cleaning up test data...")
    cleanup_success = state_manager.delete_workflow_state("test-workflow-001")
    if cleanup_success:
        print("✅ Test workflow state cleaned up")
    
    print("\n🎉 Base infrastructure test completed!")
    print("=" * 50)

async def test_agent_with_api():
    """Test agent with actual API call (if API key is available)."""
    print("\n🌐 Testing Agent with API Call...")
    
    config = BMadConfig()
    google_key = config.get_api_key('google_ai')
    
    if not google_key:
        print("⚠️  Skipping API test - Google AI API key not configured")
        return
    
    try:
        # Create an agent for testing
        test_agent = BMadAgent(
            role="test-analyst",
            model="gemini-2.5-flash",
            system_prompt="You are a test analyst. Respond with a brief analysis of the given input."
        )
        
        # Create a test request
        test_request = {
            'id': 'test-req-001',
            'message': 'Analyze this simple test: What are the key components of a successful software project?',
            'type': 'analysis'
        }
        
        print("Sending test request to agent...")
        response = await test_agent.process_request(test_request)
        
        if response['status'] == 'success':
            print("✅ Agent API call successful!")
            print(f"Response preview: {str(response['result'])[:200]}...")
        else:
            print(f"❌ Agent API call failed: {response.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error during API test: {str(e)}")

def main():
    """Main test function."""
    print("BMad Pydantic AI Agents - Base Infrastructure Test")
    print("" * 60)
    
    # Run base infrastructure tests
    asyncio.run(test_base_infrastructure())
    
    # Ask user if they want to test API calls
    try:
        user_input = input("\nDo you want to test API calls? (y/N): ").strip().lower()
        if user_input in ['y', 'yes']:
            asyncio.run(test_agent_with_api())
        else:
            print("Skipping API tests.")
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
    
    print("\n✨ All tests completed!")

if __name__ == "__main__":
    main()