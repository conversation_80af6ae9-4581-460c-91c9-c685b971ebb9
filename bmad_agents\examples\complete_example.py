#!/usr/bin/env python3
"""Complete Example: BMad Pydantic AI Agents Usage

This example demonstrates the comprehensive usage of the BMad Pydantic AI Agents system,
including:
1. Using the BMadOrchestrator for request routing
2. Direct agent usage
3. Workflow execution with BrownfieldFullstackWorkflow
4. Help system and configuration
5. Performance monitoring
6. Error handling and best practices

Run this example to see the full capabilities of the BMad agents system.
"""

import asyncio
import logging
from pathlib import Path
from typing import Dict, Any

# Import BMad components
from bmad_agents import (
    BMadOrchestrator,
    AnalystAgent,
    ArchitectAgent,
    BrownfieldFullstackWorkflow,
    config,
    setup_logging,
    performance_monitor,
    PerformanceMetrics,
    # Communication models
    AgentRequest,
    MessageType,
    Priority,
    # Production readiness imports
    BMadError,
    AgentTimeoutError,
    AgentExecutionError,
    WorkflowError,
    with_retry,
    with_timeout,
    CircuitBreaker
)
# Import communication models from main package
# from bmad_agents.base.communication import AgentRequest, MessageType, Priority

# Configure logging
setup_logging()
logger = logging.getLogger('bmad.example')

class BMadExampleRunner:
    """Example runner demonstrating BMad agents usage."""
    
    def __init__(self):
        self.orchestrator = None
        self.project_path = Path.cwd()
        
    async def initialize_system(self):
        """Initialize the BMad agents system."""
        logger.info("Initializing BMad Pydantic AI Agents system...")
        
        # Create orchestrator
        self.orchestrator = BMadOrchestrator()
        
        # Configure system (using global config dictionary approach)
        config._global_config.update({
            'project_path': str(self.project_path),
            'log_level': 'INFO',
            'enable_monitoring': True,
            'max_concurrent_agents': 3
        })
        
        logger.info("System initialized successfully")
    
    async def demonstrate_orchestrator_routing(self):
        """Demonstrate BMadOrchestrator request routing capabilities."""
        logger.info("\n=== Demonstrating Orchestrator Request Routing ===")
        
        # Example requests that will be routed to different agents
        test_requests = [
            {
                'content': 'Analyze the current codebase structure and identify technical debt',
                'expected_agent': 'analyst'
            },
            {
                'content': 'Design a microservices architecture for this monolithic application',
                'expected_agent': 'architect'
            },
            {
                'content': 'Help me understand how to use the BMad agents system',
                'expected_agent': 'help'
            }
        ]
        
        for i, request_info in enumerate(test_requests, 1):
            logger.info(f"\nRequest {i}: {request_info['content']}")
            
            try:
                # Create agent request
                request = AgentRequest(
                    request_id=f"orchestrator_req_{i}",
                    agent_role="orchestrator",
                    action="routing",
                    parameters={"content": request_info['content']},
                    context={'project_path': str(self.project_path), 'requester': 'example_system'}
                )
                
                # Route request through orchestrator
                response = await self.orchestrator.route_request(request)
                
                logger.info(f"Orchestrator action: {response.action if hasattr(response, 'action') else 'Unknown'}")
                logger.info(f"Target agent: {response.target_agent if hasattr(response, 'target_agent') else 'Unknown'}")
                logger.info(f"Message: {response.message[:200] if hasattr(response, 'message') else str(response)[:200]}...")
                
                # Verify routing (in real usage, this would be automatic)
                if request_info['expected_agent'] in response.agent_id.lower():
                    logger.info("[OK] Request routed correctly")
                else:
                    logger.warning(f"[WARNING] Expected {request_info['expected_agent']}, got {response.agent_id}")
                    
            except Exception as e:
                logger.error(f"Error processing request {i}: {e}")
    
    async def demonstrate_direct_agent_usage(self):
        """Demonstrate direct usage of individual agents."""
        logger.info("\n=== Demonstrating Direct Agent Usage ===")
        
        # Create and use AnalystAgent directly
        logger.info("\nUsing AnalystAgent directly:")
        analyst = AnalystAgent()
        
        analyst_request = {
            'id': 'analyst_req_1',
            'type': 'analysis',
            'message': 'Analyze the project structure and provide recommendations for improvement',
            'project_path': str(self.project_path),
            'analysis_type': 'structure',
            'requester': 'example_system'
        }
        
        try:
            analyst_response = await analyst.process_request(analyst_request)
            logger.info(f"Analyst Response: {str(analyst_response)[:300]}...")
            
            if hasattr(analyst_response, 'metadata') and analyst_response.metadata:
                logger.info(f"Analysis metadata: {analyst_response.metadata}")
                
        except Exception as e:
            logger.error(f"Error with AnalystAgent: {e}")
        
        # Create and use ArchitectAgent directly
        logger.info("\nUsing ArchitectAgent directly:")
        architect = ArchitectAgent()
        
        architect_request = {
            'id': 'architect_req_1',
            'type': 'design',
            'message': 'Design a scalable architecture for a brownfield e-commerce application',
            'project_type': 'e-commerce',
            'current_architecture': 'monolithic',
            'target_architecture': 'microservices',
            'requester': 'example_system'
        }
        
        try:
            architect_response = await architect.process_request(architect_request)
            logger.info(f"Architect Response: {str(architect_response)[:300]}...")
            
            if hasattr(architect_response, 'metadata') and architect_response.metadata:
                logger.info(f"Architecture metadata: {architect_response.metadata}")
                
        except Exception as e:
            logger.error(f"Error with ArchitectAgent: {e}")
    
    async def demonstrate_workflow_execution(self):
        """Demonstrate workflow execution with BrownfieldFullstackWorkflow."""
        logger.info("\n=== Demonstrating Workflow Execution ===")
        
        # Create and configure workflow
        workflow = BrownfieldFullstackWorkflow()
        
        # Workflow configuration
        workflow_config = {
            'project_path': str(self.project_path),
            'target_framework': 'FastAPI',
            'database': 'PostgreSQL',
            'frontend_framework': 'React',
            'deployment_target': 'Docker',
            'enable_monitoring': True
        }
        
        try:
            logger.info("Starting brownfield fullstack workflow...")
            
            # Configure workflow parameters
            workflow_config.update({
                'enhancement_type': 'modernization',
                'enhancement_description': 'Modernize this legacy application to use modern fullstack architecture'
            })
            
            # Start workflow
            state = await workflow.start_workflow(workflow_config)
            logger.info(f"Workflow started: {state.workflow_id}")
            logger.info(f"Current step: {state.current_step}")
            
            # Execute first step as example
            step_input = {
                'enhancement_description': workflow_config['enhancement_description'],
                'project_context': workflow_config
            }
            
            result = await workflow.execute_step(state.workflow_id, step_input)
            logger.info(f"Step executed successfully")
            logger.info(f"Step result keys: {list(result.keys()) if result else 'None'}")
            
            # Display workflow state
            if hasattr(state, 'steps_completed'):
                logger.info(f"Steps completed: {state.steps_completed}")
                
        except Exception as e:
            logger.error(f"Workflow execution error: {e}")
    
    async def demonstrate_help_system(self):
        """Demonstrate the help system capabilities."""
        logger.info("\n=== Demonstrating Help System ===")
        
        help_requests = [
            "How do I use the BMad agents system?",
            "What agents are available?",
            "How do I configure the system?",
            "What workflows can I use?",
            "How do I monitor performance?"
        ]
        
        for help_query in help_requests:
            logger.info(f"\nHelp Query: {help_query}")
            
            try:
                request = AgentRequest(
                    request_id=f"help_req_{help_requests.index(help_query) + 1}",
                    agent_role="help",
                    action="help",
                    parameters={"content": help_query},
                    context={'requester': 'example_system'}
                )
                
                response = await self.orchestrator.route_request(request)
                logger.info(f"Action: {response.action if hasattr(response, 'action') else 'Unknown'}")
                logger.info(f"Message: {response.message if hasattr(response, 'message') else response}")
                
            except Exception as e:
                logger.error(f"Help system error: {e}")
    
    def demonstrate_performance_monitoring(self):
        """Demonstrate performance monitoring capabilities."""
        logger.info("\n=== Demonstrating Performance Monitoring ===")
        
        # Get performance summary
        summary = performance_monitor.get_metrics_summary()
        logger.info(f"Performance Summary:")
        logger.info(f"  Total operations: {summary['total_operations']}")
        logger.info(f"  Success rate: {summary['success_rate']:.2%}")
        logger.info(f"  Average duration: {summary['average_duration']:.2f}s")
        logger.info(f"  Average memory usage: {summary['average_memory_usage']:.1f}MB")
        
        # Get recent errors
        recent_errors = performance_monitor.get_recent_errors(limit=3)
        if recent_errors:
            logger.info(f"\nRecent errors ({len(recent_errors)}):")
            for error in recent_errors:
                logger.info(f"  {error['timestamp']}: {error['operation']} - {error['error_message']}")
        else:
            logger.info("\nNo recent errors found")
        
        # Get agent performance ranking
        ranking = performance_monitor.get_agent_performance_ranking()
        if ranking:
            logger.info(f"\nAgent Performance Ranking:")
            for i, agent_perf in enumerate(ranking[:3], 1):
                logger.info(
                    f"  {i}. {agent_perf['agent_role']}: "
                    f"Score {agent_perf['performance_score']:.2f}, "
                    f"Success Rate {agent_perf['success_rate']:.2%}"
                )
    
    async def demonstrate_error_handling(self):
        """Demonstrate error handling and recovery."""
        logger.info("\n=== Demonstrating Error Handling ===")
        
        # Test with invalid request
        logger.info("\nTesting error handling with invalid request:")
        
        try:
            invalid_request = AgentRequest(
                request_id="invalid_req_1",
                agent_role="test",
                action="test",
                parameters={"content": ""},  # Empty content should trigger validation error
                context={'requester': 'example_system'}
            )
            
            response = await self.orchestrator.route_request(invalid_request)
            logger.info(f"Unexpected success: {response.content}")
            
        except Exception as e:
            logger.info(f"[OK] Error handled correctly: {type(e).__name__}: {e}")
        
        # Test with timeout scenario (simulated)
        logger.info("\nTesting timeout handling:")
        
        try:
            timeout_request = AgentRequest(
                request_id="timeout_req_1",
                agent_role="test",
                action="test",
                parameters={"content": "This is a test request that might timeout"},
                context={'timeout': 0.1, 'requester': 'example_system'}  # Very short timeout
            )
            
            response = await self.orchestrator.route_request(timeout_request)
            logger.info(f"Request completed: {response.content[:100]}...")
            
        except Exception as e:
            logger.info(f"[OK] Timeout handled: {type(e).__name__}: {e}")
    
    async def demonstrate_production_error_handling(self):
        """Demonstrate production-ready error handling features."""
        logger.info("\n=== Production Error Handling Demonstration ===")
        
        # Demonstrate retry decorator
        @with_retry(max_retries=3, backoff_factor=0.5)
        async def flaky_operation():
            import random
            if random.random() < 0.7:  # 70% chance of failure
                raise AgentExecutionError("Simulated agent failure")
            return "Operation succeeded after retries"
        
        try:
            result = await flaky_operation()
            logger.info(f"[OK] Retry operation result: {result}")
        except AgentExecutionError as e:
            logger.info(f"[OK] Retry operation failed after all attempts: {e}")
        
        # Demonstrate timeout decorator
        @with_timeout(timeout=1)
        async def slow_operation():
            await asyncio.sleep(2)  # This will timeout
            return "This won't be reached"
        
        try:
            result = await slow_operation()
            logger.info(f"Timeout operation result: {result}")
        except AgentTimeoutError as e:
            logger.info(f"[OK] Timeout handled correctly: {e}")
        
        # Demonstrate circuit breaker
        circuit_breaker = CircuitBreaker(failure_threshold=2, recovery_timeout=5)
        
        async def unreliable_service():
            raise AgentExecutionError("Service unavailable")
        
        logger.info("Testing circuit breaker pattern:")
        for i in range(4):
            try:
                await circuit_breaker.call(unreliable_service)
            except (AgentExecutionError, BMadError) as e:
                logger.info(f"  Attempt {i+1}: {type(e).__name__} - {e}")
                if "Circuit breaker is open" in str(e):
                    logger.info("  [OK] Circuit breaker opened successfully")
                    break
    
    def display_system_information(self):
        """Display system information and configuration."""
        logger.info("\n=== System Information ===")
        
        # Display configuration
        logger.info(f"Configuration:")
        for key, value in config._global_config.items():
            logger.info(f"  {key}: {value}")
        
        # Display available agents
        if hasattr(self.orchestrator, 'get_available_agents'):
            agents = self.orchestrator.get_available_agents()
            logger.info(f"\nAvailable Agents: {', '.join(agents)}")
        
        # Display system status
        logger.info(f"\nSystem Status:")
        logger.info(f"  Project Path: {self.project_path}")
        logger.info(f"  Monitoring Enabled: {config.get_global_config('enable_monitoring') or False}")
        logger.info(f"  Log Level: {config.get_global_config('log_level') or 'INFO'}")
    
    async def run_complete_example(self):
        """Run the complete example demonstrating all features."""
        logger.info("Starting BMad Pydantic AI Agents Complete Example")
        logger.info("=" * 60)
        
        try:
            # Initialize system
            await self.initialize_system()
            
            # Display system information
            self.display_system_information()
            
            # Run demonstrations
            await self.demonstrate_orchestrator_routing()
            await self.demonstrate_direct_agent_usage()
            await self.demonstrate_workflow_execution()
            await self.demonstrate_help_system()
            await self.demonstrate_error_handling()
            await self.demonstrate_production_error_handling()
            
            # Show performance monitoring
            self.demonstrate_performance_monitoring()
            
            logger.info("\n" + "=" * 60)
            logger.info("Complete example finished successfully!")
            
        except Exception as e:
            logger.error(f"Example execution failed: {e}")
            raise

def main():
    """Main entry point for the complete example."""
    print("BMad Pydantic AI Agents - Complete Usage Example")
    print("=" * 50)
    print("This example demonstrates:")
    print("• Orchestrator request routing")
    print("• Direct agent usage")
    print("• Workflow execution")
    print("• Help system")
    print("• Performance monitoring")
    print("• Error handling")
    print("• Production error handling (retry, timeout, circuit breaker)")
    print("=" * 50)
    
    # Run the example
    runner = BMadExampleRunner()
    
    try:
        asyncio.run(runner.run_complete_example())
    except KeyboardInterrupt:
        print("\nExample interrupted by user")
    except Exception as e:
        print(f"\nExample failed with error: {e}")
        logging.exception("Detailed error information:")

if __name__ == "__main__":
    main()