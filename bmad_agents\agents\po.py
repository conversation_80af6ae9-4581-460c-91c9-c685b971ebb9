from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from ..base.bmad_agent import BMadAgent

class ProductVision(BaseModel):
    vision_statement: str
    target_audience: List[str]
    value_proposition: str
    key_features: List[str]
    success_metrics: List[str]
    competitive_advantages: List[str]
    market_positioning: str

class Epic(BaseModel):
    epic_id: str
    title: str
    description: str
    business_value: str
    acceptance_criteria: List[str]
    user_stories: List[str] = Field(default_factory=list)
    priority: int = Field(ge=1, le=10)
    effort_estimate: str  # XS, S, M, L, XL
    dependencies: List[str] = Field(default_factory=list)
    status: str = "backlog"  # backlog, in_progress, done

class UserStoryBacklog(BaseModel):
    story_id: str
    title: str
    description: str
    acceptance_criteria: List[str]
    business_value: int = Field(ge=1, le=10)
    effort_points: int = Field(ge=1, le=21)  # Fibonacci sequence
    priority_rank: int
    epic_id: Optional[str] = None
    dependencies: List[str] = Field(default_factory=list)
    status: str = "backlog"
    sprint_assignment: Optional[str] = None

class ReleaseCandidate(BaseModel):
    release_id: str
    version: str
    target_date: date
    theme: str
    included_epics: List[str]
    key_features: List[str]
    success_criteria: List[str]
    risk_assessment: List[str]
    go_no_go_criteria: List[str]

class StakeholderFeedback(BaseModel):
    stakeholder_type: str  # customer, internal, partner
    feedback_category: str  # feature_request, bug_report, enhancement
    description: str
    priority: str  # low, medium, high, critical
    impact_assessment: str
    implementation_effort: str
    recommendation: str

class ProductBacklog(BaseModel):
    product_name: str
    vision: ProductVision
    epics: List[Epic]
    user_stories: List[UserStoryBacklog]
    releases: List[ReleaseCandidate]
    total_story_points: int
    prioritization_criteria: List[str]
    last_updated: datetime = Field(default_factory=datetime.now)

class BacklogRefinement(BaseModel):
    refined_stories: List[UserStoryBacklog]
    new_stories: List[UserStoryBacklog]
    removed_stories: List[str]
    priority_changes: Dict[str, int]
    effort_updates: Dict[str, int]
    recommendations: List[str]

class POAgent(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are an expert Product Owner in the BMad Method framework.
        
        Your responsibilities include:
        - Defining and communicating product vision and strategy
        - Managing and prioritizing the product backlog
        - Writing clear user stories with acceptance criteria
        - Collaborating with stakeholders to gather requirements
        - Making product decisions based on business value
        - Planning releases and defining success criteria
        
        Always focus on maximizing business value and user satisfaction.
        Ensure clear communication of requirements and priorities.
        Balance stakeholder needs with technical constraints and market opportunities.
        """
        
        super().__init__(
            role="po",
            system_prompt=system_prompt,
            response_model=ProductVision
        )
    
    async def define_product_vision(self, market_research: str, stakeholder_input: str) -> ProductVision:
        """Define comprehensive product vision based on market research and stakeholder input."""
        prompt = f"""
        Define a comprehensive product vision based on the following inputs:
        
        Market Research:
        {market_research}
        
        Stakeholder Input:
        {stakeholder_input}
        
        Create a product vision that includes:
        - Clear and inspiring vision statement
        - Well-defined target audience segments
        - Compelling value proposition
        - Key features that differentiate the product
        - Measurable success metrics and KPIs
        - Competitive advantages and market positioning
        - Strategic market positioning
        """
        
        return await self.execute_with_logging(prompt)
    
    async def create_product_backlog(self, vision: ProductVision, requirements: List[str]) -> ProductBacklog:
        """Create comprehensive product backlog with epics and user stories."""
        prompt = f"""
        Create a comprehensive product backlog based on the product vision and requirements:
        
        Product Vision:
        - Vision: {vision.vision_statement}
        - Target Audience: {', '.join(vision.target_audience)}
        - Key Features: {', '.join(vision.key_features)}
        
        Requirements:
        {chr(10).join(requirements)}
        
        Create a structured backlog including:
        - High-level epics with business value and effort estimates
        - Detailed user stories with acceptance criteria
        - Priority ranking based on business value and dependencies
        - Release planning with target dates and themes
        - Story point estimates using Fibonacci sequence
        - Clear prioritization criteria
        """
        
        temp_agent = BMadAgent(
            role="po-backlog",
            system_prompt=self.system_prompt,
            response_model=ProductBacklog
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def prioritize_backlog(self, backlog: ProductBacklog, criteria: Dict[str, Any]) -> List[UserStoryBacklog]:
        """Prioritize backlog items based on business value and criteria."""
        stories_summary = "\n".join([f"- {story.title}: Value={story.business_value}, Effort={story.effort_points}" for story in backlog.user_stories])
        
        prompt = f"""
        Prioritize the product backlog based on the following criteria:
        
        Current Stories:
        {stories_summary}
        
        Prioritization Criteria:
        {criteria}
        
        Apply prioritization considering:
        - Business value and ROI
        - User impact and satisfaction
        - Technical dependencies and risks
        - Market timing and competitive factors
        - Resource availability and effort estimates
        - Strategic alignment with product vision
        
        Return stories in prioritized order with updated priority ranks.
        """
        
        class PrioritizedBacklog(BaseModel):
            stories: List[UserStoryBacklog]
        
        temp_agent = BMadAgent(
            role="po-prioritization",
            system_prompt=self.system_prompt,
            response_model=PrioritizedBacklog
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.stories
    
    async def refine_backlog(self, current_backlog: List[UserStoryBacklog], new_insights: str) -> BacklogRefinement:
        """Refine backlog based on new insights and feedback."""
        current_stories = "\n".join([f"- {story.title}: {story.description}" for story in current_backlog])
        
        prompt = f"""
        Refine the product backlog based on new insights and feedback:
        
        Current Backlog:
        {current_stories}
        
        New Insights:
        {new_insights}
        
        Provide backlog refinement including:
        - Updated story descriptions and acceptance criteria
        - New stories to be added based on insights
        - Stories to be removed or deprioritized
        - Priority changes with justification
        - Effort estimate updates
        - Recommendations for next steps
        """
        
        temp_agent = BMadAgent(
            role="po-refinement",
            system_prompt=self.system_prompt,
            response_model=BacklogRefinement
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def plan_release(self, backlog: List[UserStoryBacklog], constraints: Dict[str, Any]) -> ReleaseCandidate:
        """Plan product release based on backlog and constraints."""
        high_priority_stories = [story for story in backlog if story.priority_rank <= 10]
        stories_summary = "\n".join([f"- {story.title}: {story.effort_points} points" for story in high_priority_stories])
        
        prompt = f"""
        Plan a product release based on high-priority backlog items and constraints:
        
        High-Priority Stories:
        {stories_summary}
        
        Constraints:
        {constraints}
        
        Create a release plan including:
        - Release theme and version number
        - Target date based on team velocity and constraints
        - Included epics and key features
        - Success criteria and metrics
        - Risk assessment and mitigation
        - Go/no-go criteria for release decision
        """
        
        temp_agent = BMadAgent(
            role="po-release",
            system_prompt=self.system_prompt,
            response_model=ReleaseCandidate
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def analyze_stakeholder_feedback(self, feedback_data: List[str]) -> List[StakeholderFeedback]:
        """Analyze stakeholder feedback and provide recommendations."""
        feedback_text = "\n".join([f"- {feedback}" for feedback in feedback_data])
        
        prompt = f"""
        Analyze the following stakeholder feedback and provide structured recommendations:
        
        Feedback Data:
        {feedback_text}
        
        For each piece of feedback, provide:
        - Stakeholder type classification
        - Feedback category (feature request, bug report, enhancement)
        - Clear description and context
        - Priority assessment based on impact and frequency
        - Business impact assessment
        - Implementation effort estimate
        - Specific recommendation for action
        """
        
        class StakeholderFeedbackList(BaseModel):
            feedback_items: List[StakeholderFeedback]
        
        temp_agent = BMadAgent(
            role="po-feedback",
            system_prompt=self.system_prompt,
            response_model=StakeholderFeedbackList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.feedback_items
    
    async def validate_user_stories(self, stories: List[UserStoryBacklog]) -> Dict[str, Any]:
        """Validate user stories for completeness and quality."""
        stories_text = "\n".join([f"Story: {story.title}\nDescription: {story.description}\nAC: {', '.join(story.acceptance_criteria)}" for story in stories])
        
        prompt = f"""
        Validate the following user stories for completeness and quality:
        
        {stories_text}
        
        Check each story for:
        - Clear user role and goal definition
        - Specific and testable acceptance criteria
        - Appropriate size and complexity
        - Independence from other stories
        - Business value articulation
        - Completeness of requirements
        
        Provide validation results with specific improvement recommendations.
        """
        
        class StoryValidation(BaseModel):
            valid_stories: List[str]
            stories_needing_improvement: Dict[str, List[str]]
            overall_quality_score: int = Field(ge=1, le=10)
            improvement_recommendations: List[str]
        
        temp_agent = BMadAgent(
            role="po-validation",
            system_prompt=self.system_prompt,
            response_model=StoryValidation
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.model_dump()