from typing import Dict, List, Any
from .base_workflow import BaseWorkflow
from ..base.models import WorkflowStep, WorkflowState
from ..agents.analyst import AnalystAgent
from ..agents.architect import ArchitectAgent
from ..agents.po import POAgent
from ..agents.pm import PMAgent
from ..agents.developer import DeveloperAgent
from ..agents.qa import QAAgent
from ..agents.ux import UXAgent
from ..agents.devops import DevOpsAgent

class GreenfieldFullstackWorkflow(BaseWorkflow):
    """Workflow for creating new full-stack applications from scratch."""
    
    def __init__(self):
        super().__init__("greenfield-fullstack")
        
        # Initialize agents
        self.analyst = AnalystAgent()
        self.architect = ArchitectAgent()
        self.po = POAgent()
        self.pm = PMAgent()
        self.developer = DeveloperAgent()
        self.qa = QAAgent()
        self.ux = UXAgent()
        self.devops = DevOpsAgent()
    
    def _define_steps(self) -> List[WorkflowStep]:
        return [
            WorkflowStep(
                step_id="requirements_gathering",
                step_name="Requirements Gathering",
                required_agents=["analyst", "po"],
                input_requirements={"project_vision": "string", "stakeholder_input": "dict"},
                output_specifications={"requirements_document": "dict", "user_personas": "list"}
            ),
            WorkflowStep(
                step_id="ux_design",
                step_name="UX Design",
                required_agents=["ux"],
                input_requirements={"requirements": "dict", "user_personas": "list"},
                output_specifications={"wireframes": "dict", "user_flows": "list", "design_system": "dict"}
            ),
            WorkflowStep(
                step_id="architecture_design",
                step_name="Architecture Design",
                required_agents=["architect"],
                input_requirements={"requirements": "dict", "ux_design": "dict"},
                output_specifications={"system_architecture": "dict", "technology_stack": "dict"}
            ),
            WorkflowStep(
                step_id="prd_creation",
                step_name="PRD Creation",
                required_agents=["po"],
                input_requirements={"requirements": "dict", "architecture": "dict", "ux_design": "dict"},
                output_specifications={"prd_document": "dict"}
            ),
            WorkflowStep(
                step_id="project_planning",
                step_name="Project Planning",
                required_agents=["pm"],
                input_requirements={"prd": "dict", "architecture": "dict"},
                output_specifications={"project_plan": "dict", "timeline": "dict", "resource_allocation": "dict"}
            ),
            WorkflowStep(
                step_id="story_creation",
                step_name="Story Creation",
                required_agents=["analyst", "po"],
                input_requirements={"prd": "dict", "project_plan": "dict"},
                output_specifications={"user_stories": "list", "backlog": "dict"}
            ),
            WorkflowStep(
                step_id="development_setup",
                step_name="Development Environment Setup",
                required_agents=["developer", "devops"],
                input_requirements={"architecture": "dict", "technology_stack": "dict"},
                output_specifications={"dev_environment": "dict", "ci_cd_pipeline": "dict"}
            ),
            WorkflowStep(
                step_id="qa_planning",
                step_name="QA Planning",
                required_agents=["qa"],
                input_requirements={"user_stories": "list", "architecture": "dict"},
                output_specifications={"test_strategy": "dict", "test_cases": "list"}
            )
        ]
    
    async def _execute_step_logic(self, step: WorkflowStep, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute step-specific logic."""
        
        if step.step_id == "requirements_gathering":
            return await self._execute_requirements_gathering(step_input, state)
        elif step.step_id == "ux_design":
            return await self._execute_ux_design(step_input, state)
        elif step.step_id == "architecture_design":
            return await self._execute_architecture_design(step_input, state)
        elif step.step_id == "prd_creation":
            return await self._execute_prd_creation(step_input, state)
        elif step.step_id == "project_planning":
            return await self._execute_project_planning(step_input, state)
        elif step.step_id == "story_creation":
            return await self._execute_story_creation(step_input, state)
        elif step.step_id == "development_setup":
            return await self._execute_development_setup(step_input, state)
        elif step.step_id == "qa_planning":
            return await self._execute_qa_planning(step_input, state)
        else:
            raise ValueError(f"Unknown step: {step.step_id}")
    
    async def _execute_requirements_gathering(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute requirements gathering step."""
        project_vision = step_input.get("project_vision", "")
        stakeholder_input = step_input.get("stakeholder_input", {})
        
        # Analyst analyzes requirements
        requirements_prompt = f"""
        Gather and analyze requirements for this new project:
        Project Vision: {project_vision}
        Stakeholder Input: {stakeholder_input}
        
        Provide comprehensive analysis including:
        1. Functional requirements
        2. Non-functional requirements
        3. Business objectives
        4. Success criteria
        5. Constraints and assumptions
        """
        
        analyst_result = await self.analyst.analyze_requirements(
            requirements_prompt,
            context=state.shared_context
        )
        
        # PO creates user personas
        persona_prompt = f"""
        Create user personas based on the requirements analysis:
        Analysis: {analyst_result.model_dump()}
        
        Generate detailed user personas including:
        1. Demographics
        2. Goals and motivations
        3. Pain points
        4. User journey touchpoints
        """
        
        personas_result = await self.po.create_user_personas(
            persona_prompt,
            context=state.shared_context
        )
        
        requirements_document = analyst_result.model_dump()
        user_personas = personas_result
        
        return {
            "requirements_document": requirements_document,
            "user_personas": [persona.model_dump() for persona in user_personas],
            "context_updates": {
                "requirements": requirements_document,
                "personas": [persona.model_dump() for persona in user_personas]
            }
        }
    
    async def _execute_ux_design(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute UX design step."""
        requirements = step_input.get("requirements", state.shared_context.get("requirements", {}))
        user_personas = step_input.get("user_personas", state.shared_context.get("personas", []))
        
        ux_prompt = f"""
        Design the user experience for this application:
        Requirements: {requirements}
        User Personas: {user_personas}
        
        Create:
        1. User flow diagrams
        2. Wireframes for key screens
        3. Design system guidelines
        4. Interaction patterns
        5. Accessibility considerations
        """
        
        ux_result = await self.ux.create_ux_design(
            ux_prompt,
            context=state.shared_context
        )
        
        return {
            "wireframes": ux_result.wireframes,
            "user_flows": ux_result.user_flows,
            "design_system": ux_result.design_system.model_dump(),
            "context_updates": {
                "ux_design": ux_result.model_dump()
            }
        }
    
    async def _execute_architecture_design(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute architecture design step."""
        requirements = step_input.get("requirements", state.shared_context.get("requirements", {}))
        ux_design = step_input.get("ux_design", state.shared_context.get("ux_design", {}))
        
        architecture_prompt = f"""
        Design the system architecture for this new application:
        Requirements: {requirements}
        UX Design: {ux_design}
        
        Provide:
        1. High-level system architecture
        2. Technology stack recommendations
        3. Database design
        4. API design
        5. Security considerations
        6. Scalability planning
        """
        
        architecture_result = await self.architect.design_architecture(
            architecture_prompt
        )
        
        return {
            "system_architecture": architecture_result.model_dump(),
            "technology_stack": architecture_result.technology_stack,
            "context_updates": {
                "architecture": architecture_result.model_dump()
            }
        }
    
    async def _execute_prd_creation(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute PRD creation step."""
        requirements = step_input.get("requirements", state.shared_context.get("requirements", {}))
        architecture = step_input.get("architecture", state.shared_context.get("architecture", {}))
        ux_design = step_input.get("ux_design", state.shared_context.get("ux_design", {}))
        
        prd_prompt = f"""
        Create a comprehensive Product Requirements Document (PRD):
        Requirements: {requirements}
        Architecture: {architecture}
        UX Design: {ux_design}
        
        Include:
        1. Executive Summary
        2. Product Overview
        3. Feature Specifications
        4. Technical Requirements
        5. Success Metrics
        6. Launch Plan
        """
        
        prd_result = await self.po.create_prd(
            prd_prompt,
            context=state.shared_context
        )
        
        return {
            "prd_document": prd_result.model_dump(),
            "context_updates": {
                "prd": prd_result.model_dump()
            }
        }
    
    async def _execute_project_planning(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute project planning step."""
        prd = step_input.get("prd", state.shared_context.get("prd", {}))
        architecture = step_input.get("architecture", state.shared_context.get("architecture", {}))
        
        planning_prompt = f"""
        Create a comprehensive project plan:
        PRD: {prd}
        Architecture: {architecture}
        
        Provide:
        1. Project timeline with milestones
        2. Resource allocation plan
        3. Risk assessment and mitigation
        4. Communication plan
        5. Quality assurance strategy
        """
        
        planning_result = await self.pm.create_project_plan(
            planning_prompt,
            context=state.shared_context
        )
        
        return {
            "project_plan": planning_result.model_dump(),
            "timeline": planning_result.timeline,
            "resource_allocation": planning_result.resource_allocation,
            "context_updates": {
                "project_plan": planning_result.model_dump()
            }
        }
    
    async def _execute_story_creation(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute story creation step."""
        prd = step_input.get("prd", state.shared_context.get("prd", {}))
        project_plan = step_input.get("project_plan", state.shared_context.get("project_plan", {}))
        
        story_prompt = f"""
        Create detailed user stories based on:
        PRD: {prd}
        Project Plan: {project_plan}
        
        Generate:
        1. Epic-level stories
        2. Detailed user stories with acceptance criteria
        3. Technical stories
        4. Story prioritization
        5. Sprint planning
        """
        
        stories_result = await self.po.create_user_stories(
            story_prompt,
            context=state.shared_context
        )
        
        backlog = {
            "total_stories": len(stories_result),
            "epics": [story for story in stories_result if story.effort_estimate == "Large"],
            "ready_stories": [story for story in stories_result if story.priority == "High"]
        }
        
        return {
            "user_stories": [story.model_dump() for story in stories_result],
            "backlog": backlog,
            "context_updates": {
                "stories": {
                    "user_stories": [story.model_dump() for story in stories_result],
                    "backlog": backlog
                }
            }
        }
    
    async def _execute_development_setup(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute development setup step."""
        architecture = step_input.get("architecture", state.shared_context.get("architecture", {}))
        technology_stack = step_input.get("technology_stack", {})
        
        dev_setup_prompt = f"""
        Set up the development environment:
        Architecture: {architecture}
        Technology Stack: {technology_stack}
        
        Provide:
        1. Development environment configuration
        2. Project structure
        3. Development tools and IDE setup
        4. Local development workflow
        """
        
        dev_result = await self.developer.setup_development_environment(
            dev_setup_prompt,
            context=state.shared_context
        )
        
        devops_prompt = f"""
        Set up CI/CD pipeline:
        Architecture: {architecture}
        Development Setup: {dev_result.model_dump()}
        
        Configure:
        1. Continuous integration pipeline
        2. Deployment automation
        3. Monitoring and logging
        4. Infrastructure as code
        """
        
        devops_result = await self.devops.setup_cicd_pipeline(
            devops_prompt,
            context=state.shared_context
        )
        
        return {
            "dev_environment": dev_result.model_dump(),
            "ci_cd_pipeline": devops_result.model_dump(),
            "context_updates": {
                "development": {
                    "environment": dev_result.model_dump(),
                    "cicd": devops_result.model_dump()
                }
            }
        }
    
    async def _execute_qa_planning(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute QA planning step."""
        user_stories = step_input.get("user_stories", state.shared_context.get("stories", {}).get("user_stories", []))
        architecture = step_input.get("architecture", state.shared_context.get("architecture", {}))
        
        qa_prompt = f"""
        Create comprehensive QA strategy:
        User Stories: {user_stories}
        Architecture: {architecture}
        
        Develop:
        1. Test strategy and approach
        2. Test cases for each user story
        3. Automated testing plan
        4. Performance testing strategy
        5. Security testing approach
        """
        
        qa_result = await self.qa.create_test_strategy(
            qa_prompt,
            context=state.shared_context
        )
        
        return {
            "test_strategy": qa_result.model_dump(),
            "test_cases": qa_result.test_cases,
            "context_updates": {
                "qa": {
                    "strategy": qa_result.model_dump(),
                    "test_cases": qa_result.test_cases
                }
            }
        }