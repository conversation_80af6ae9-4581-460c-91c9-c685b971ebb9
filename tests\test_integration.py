import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from bmad_agents.agents.orchestrator import BMadOrchestrator
from bmad_agents.workflows.brownfield_fullstack import BrownfieldFullstackWorkflow
from bmad_agents.base.models import WorkflowState, AgentResponse, ResponseStatus
from bmad_agents.agents.analyst import AnalystAgent, RequirementAnalysis
from bmad_agents.agents.architect import ArchitectAgent, SystemArchitecture

class TestIntegration:
    """Integration tests for BMad Pydantic AI Agents system."""
    
    @pytest.mark.asyncio
    async def test_orchestrator_agent_routing(self):
        """Test that orchestrator correctly routes requests to appropriate agents."""
        orchestrator = BMadOrchestrator()
        
        # Mock the route_request method to avoid actual AI calls
        with patch.object(orchestrator, 'route_request') as mock_route:
            mock_route.return_value = Mock(
                target_agent="analyst",
                action="analyze_requirements",
                message="I'll analyze the requirements for your new feature"
            )
            
            response = await orchestrator.route_request(
                "I need to analyze requirements for a new feature"
            )
            
            assert response.target_agent == "analyst"
            assert "analyze" in response.message.lower()
            mock_route.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_workflow_execution(self):
        """Test complete workflow execution from start to first step."""
        workflow = BrownfieldFullstackWorkflow()
        
        # Mock the workflow methods to avoid actual AI calls
        with patch.object(workflow, 'start_workflow') as mock_start, \
             patch.object(workflow, 'execute_step') as mock_execute:
            
            # Mock workflow start
            mock_state = WorkflowState(
                workflow_id="test-workflow-123",
                workflow_type="brownfield-fullstack",
                current_step="scope_classification"
            )
            mock_start.return_value = mock_state
            
            # Mock step execution
            mock_execute.return_value = {
                "scope_type": "major_enhancement",
                "complexity": "high",
                "analysis": {"summary": "Complex authentication system"}
            }
            
            # Start workflow
            state = await workflow.start_workflow({
                "project_name": "Test Project",
                "enhancement_type": "new_feature"
            })
            
            assert state.workflow_type == "brownfield-fullstack"
            assert state.current_step == "scope_classification"
            
            # Execute first step
            result = await workflow.execute_step(state.workflow_id, {
                "enhancement_description": "Add user authentication system"
            })
            
            assert "scope_type" in result
            assert "complexity" in result
            assert result["scope_type"] == "major_enhancement"
    
    @pytest.mark.asyncio
    async def test_multi_agent_coordination(self):
        """Test orchestrator coordination of multiple agents."""
        orchestrator = BMadOrchestrator()
        
        # Mock agent responses
        mock_analyst_result = RequirementAnalysis(
            summary="User management system requirements",
            functional_requirements=["User registration", "User authentication"],
            non_functional_requirements=["Security", "Performance"],
            assumptions=["OAuth2 integration"],
            risks=["Security vulnerabilities"],
            recommendations=["Use established libraries"]
        )
        
        mock_architect_result = SystemArchitecture(
            overview="Microservices architecture for user management",
            components=[],
            architectural_decisions=[],
            technology_stack={"backend": "Python", "database": "PostgreSQL"},
            deployment_considerations=["Docker containers"],
            scalability_considerations=["Horizontal scaling"],
            security_considerations=["OAuth2", "JWT tokens"]
        )
        
        with patch.object(orchestrator, 'execute_agent_request') as mock_execute:
            # Configure mock to return different results based on agent type
            def mock_agent_response(agent_type, request, context=None):
                if agent_type == "analyst":
                    return mock_analyst_result
                elif agent_type == "architect":
                    return mock_architect_result
                return None
            
            mock_execute.side_effect = mock_agent_response
            
            # Test multi-agent coordination
            analyst_result = await orchestrator.execute_agent_request(
                "analyst",
                "Analyze requirements for user management system"
            )
            
            architect_result = await orchestrator.execute_agent_request(
                "architect",
                "Design architecture based on analyst findings",
                context={"analyst_result": analyst_result}
            )
            
            assert analyst_result is not None
            assert architect_result is not None
            assert isinstance(analyst_result, RequirementAnalysis)
            assert isinstance(architect_result, SystemArchitecture)
            assert mock_execute.call_count == 2
    
    @pytest.mark.asyncio
    async def test_workflow_state_persistence(self):
        """Test that workflow state is properly persisted and retrieved."""
        workflow = BrownfieldFullstackWorkflow()
        
        with patch.object(workflow.state_manager, 'save_state') as mock_save, \
             patch.object(workflow.state_manager, 'load_state') as mock_load:
            
            # Mock state persistence
            test_state = WorkflowState(
                workflow_id="persist-test-123",
                workflow_type="brownfield-fullstack",
                current_step="scope_classification",
                shared_context={"project_name": "Test Project"}
            )
            
            mock_load.return_value = test_state
            
            # Test state loading
            loaded_state = await workflow.state_manager.load_state("persist-test-123")
            
            assert loaded_state is not None
            assert loaded_state.workflow_id == "persist-test-123"
            assert loaded_state.shared_context["project_name"] == "Test Project"
            mock_load.assert_called_once_with("persist-test-123")
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self):
        """Test error handling across the system."""
        orchestrator = BMadOrchestrator()
        
        with patch.object(orchestrator, 'execute_agent_request') as mock_execute:
            # Mock an error scenario
            mock_execute.side_effect = Exception("Simulated agent error")
            
            # Test that errors are properly handled
            with pytest.raises(Exception) as exc_info:
                await orchestrator.execute_agent_request(
                    "analyst",
                    "This should fail"
                )
            
            assert "Simulated agent error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_concurrent_agent_execution(self):
        """Test concurrent execution of multiple agents."""
        orchestrator = BMadOrchestrator()
        
        with patch.object(orchestrator, 'execute_agent_request') as mock_execute:
            # Mock delayed responses to simulate concurrent execution
            async def mock_delayed_response(agent_type, request, context=None):
                await asyncio.sleep(0.1)  # Simulate processing time
                return f"Response from {agent_type}"
            
            mock_execute.side_effect = mock_delayed_response
            
            # Execute multiple agents concurrently
            tasks = [
                orchestrator.execute_agent_request("analyst", "Task 1"),
                orchestrator.execute_agent_request("architect", "Task 2"),
                orchestrator.execute_agent_request("pm", "Task 3")
            ]
            
            results = await asyncio.gather(*tasks)
            
            assert len(results) == 3
            assert "analyst" in results[0]
            assert "architect" in results[1]
            assert "pm" in results[2]
            assert mock_execute.call_count == 3

class TestWorkflowIntegration:
    """Integration tests specifically for workflow functionality."""
    
    @pytest.mark.asyncio
    async def test_complete_workflow_cycle(self):
        """Test a complete workflow from start to completion."""
        workflow = BrownfieldFullstackWorkflow()
        
        # Mock all workflow steps
        with patch.object(workflow, '_execute_scope_classification') as mock_scope, \
             patch.object(workflow, '_execute_documentation_check') as mock_docs, \
             patch.object(workflow, '_execute_project_analysis') as mock_analysis:
            
            # Configure mock responses for each step
            mock_scope.return_value = {
                "scope_type": "major_enhancement",
                "complexity": "high",
                "next_step": "documentation_check"
            }
            
            mock_docs.return_value = {
                "documentation_status": "needs_update",
                "next_step": "project_analysis"
            }
            
            mock_analysis.return_value = {
                "analysis_complete": True,
                "next_step": "prd_creation"
            }
            
            # Start workflow
            initial_state = WorkflowState(
                workflow_id="complete-test-123",
                workflow_type="brownfield-fullstack",
                current_step="scope_classification"
            )
            
            with patch.object(workflow, 'start_workflow', return_value=initial_state):
                state = await workflow.start_workflow({
                    "project_name": "Integration Test Project",
                    "enhancement_type": "major_feature"
                })
                
                # Execute multiple steps
                step1_result = await workflow.execute_step(state.workflow_id, {
                    "enhancement_description": "Add comprehensive user management"
                })
                
                assert step1_result["scope_type"] == "major_enhancement"
                mock_scope.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_workflow_step_dependencies(self):
        """Test that workflow steps properly handle dependencies."""
        workflow = BrownfieldFullstackWorkflow()
        
        # Test that steps cannot be executed out of order
        with patch.object(workflow.state_manager, 'load_state') as mock_load:
            # Mock a state that's not ready for a particular step
            mock_state = WorkflowState(
                workflow_id="dependency-test-123",
                workflow_type="brownfield-fullstack",
                current_step="scope_classification",  # Still on first step
                completed_steps=[]  # No steps completed yet
            )
            mock_load.return_value = mock_state
            
            # Attempting to execute a later step should handle the dependency properly
            # This would be implementation-specific based on how the workflow handles dependencies
            state = await workflow.state_manager.load_state("dependency-test-123")
            assert state.current_step == "scope_classification"
            assert len(state.completed_steps) == 0

if __name__ == "__main__":
    pytest.main([__file__, "-v"])