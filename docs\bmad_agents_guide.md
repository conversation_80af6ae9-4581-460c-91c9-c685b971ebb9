# BMad Pydantic AI Agents - User Guide

## Overview

The BMad Pydantic AI Agents system implements the complete BMad Method using the Pydantic AI framework. It provides specialized AI agents for each role in the software development process, along with workflow orchestration capabilities.

This system extends the existing BMad framework with AI-powered agents that can:
- Analyze requirements and create user stories
- Design system architecture and make technical decisions
- Manage projects and coordinate development activities
- Ensure quality through testing and review processes
- Orchestrate complex multi-agent workflows

## Quick Start

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd BMADPydanticAgents
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your API keys
```

### Using Individual Agents

```python
from bmad_agents.agents.analyst import AnalystAgent
from bmad_agents.agents.architect import ArchitectAgent

# Create and use an analyst agent
analyst = AnalystAgent()
analysis = await analyst.analyze_requirements(
    "Create a web application for project management"
)

print(f"Summary: {analysis.summary}")
print(f"Functional Requirements: {analysis.functional_requirements}")

# Create and use an architect agent
architect = ArchitectAgent()
architecture = await architect.design_architecture(
    "Design scalable microservices architecture"
)

print(f"Architecture Overview: {architecture.overview}")
print(f"Components: {len(architecture.components)}")
```

### Using the Orchestrator

```python
from bmad_agents.agents.orchestrator import BMadOrchestrator

# Create orchestrator
orchestrator = BMadOrchestrator()

# Route requests automatically
response = await orchestrator.route_request(
    "I need to analyze requirements for a new feature"
)

print(f"Routed to: {response.target_agent}")
print(f"Action: {response.action}")
print(f"Message: {response.message}")

# Execute agent request directly
result = await orchestrator.execute_agent_request(
    "analyst",
    "Analyze requirements for user authentication system"
)

# Get help
help_info = await orchestrator.get_help("workflows")
print(help_info)
```

### Using Workflows

```python
from bmad_agents.workflows.brownfield_fullstack import BrownfieldFullstackWorkflow

# Start a workflow
workflow = BrownfieldFullstackWorkflow()
state = await workflow.start_workflow({
    "project_name": "My Project",
    "enhancement_description": "Add user authentication"
})

print(f"Workflow ID: {state.workflow_id}")
print(f"Current Step: {state.current_step}")

# Execute workflow steps
result = await workflow.execute_step(state.workflow_id, {
    "enhancement_description": "Add OAuth2 authentication system"
})

print(f"Step Result: {result}")
```

## Available Agents

### Analyst Agent
- **Purpose**: Requirements analysis and user story creation
- **Key Methods**: 
  - `analyze_requirements(requirements, context=None)` - Analyze business requirements
  - `create_user_stories(analysis, additional_context="")` - Create user stories from analysis
- **Output Models**: `RequirementAnalysis`, `UserStory`
- **Use Cases**: 
  - Breaking down complex requirements
  - Creating detailed user stories with acceptance criteria
  - Identifying risks and assumptions

### Architect Agent
- **Purpose**: System design and technical decisions
- **Key Methods**: 
  - `design_architecture(requirements, constraints=None)` - Design system architecture
  - `review_architecture(architecture, criteria)` - Review existing architecture
- **Output Models**: `SystemArchitecture`, `TechnicalComponent`, `ArchitecturalDecision`
- **Use Cases**: 
  - Designing scalable system architectures
  - Making technology stack decisions
  - Reviewing and optimizing existing designs

### Project Manager Agent
- **Purpose**: Project planning and coordination
- **Key Methods**: 
  - `create_project_plan(requirements, constraints=None)` - Create comprehensive project plans
  - `track_progress(project_plan, updates)` - Track and update project progress
- **Output Models**: `ProjectPlan`, `ProjectTask`, `ProjectMilestone`
- **Use Cases**: 
  - Creating detailed project timelines
  - Resource allocation and planning
  - Risk management and mitigation

### Product Owner Agent
- **Purpose**: Product strategy and backlog management
- **Key Methods**: 
  - `prioritize_backlog(user_stories, criteria)` - Prioritize product backlog
  - `define_acceptance_criteria(user_story)` - Define detailed acceptance criteria
- **Output Models**: `ProductBacklog`, `BacklogItem`, `AcceptanceCriteria`
- **Use Cases**: 
  - Managing product backlogs
  - Defining product requirements
  - Stakeholder communication

### Scrum Master Agent
- **Purpose**: Agile process facilitation
- **Key Methods**: 
  - `facilitate_planning(team_capacity, backlog)` - Facilitate sprint planning
  - `identify_impediments(team_updates)` - Identify and track impediments
- **Output Models**: `SprintPlan`, `Impediment`, `TeamMetrics`
- **Use Cases**: 
  - Sprint planning and facilitation
  - Team performance monitoring
  - Process improvement recommendations

### Developer Agent
- **Purpose**: Technical implementation guidance
- **Key Methods**: 
  - `create_implementation_plan(user_story, architecture)` - Create implementation plans
  - `review_code(code_snippet, criteria)` - Review code quality
- **Output Models**: `ImplementationPlan`, `CodeReview`, `TechnicalTask`
- **Use Cases**: 
  - Breaking down user stories into technical tasks
  - Code review and quality assessment
  - Technical implementation guidance

### QA Agent
- **Purpose**: Quality assurance and testing
- **Key Methods**: 
  - `create_test_plan(user_story, acceptance_criteria)` - Create comprehensive test plans
  - `review_test_results(test_results)` - Analyze test results
- **Output Models**: `TestPlan`, `TestCase`, `QualityReport`
- **Use Cases**: 
  - Creating detailed test strategies
  - Test case generation
  - Quality metrics and reporting

### UX Expert Agent
- **Purpose**: User experience design and optimization
- **Key Methods**: 
  - `analyze_user_journey(user_story, context)` - Analyze user experience flows
  - `create_wireframes(requirements)` - Create UX wireframes and mockups
- **Output Models**: `UserJourney`, `UXRecommendation`, `Wireframe`
- **Use Cases**: 
  - User experience analysis
  - Interface design recommendations
  - Usability optimization

### BMad Orchestrator
- **Purpose**: Multi-agent coordination and workflow management
- **Key Methods**: 
  - `route_request(request)` - Route requests to appropriate agents
  - `execute_agent_request(agent_type, request, context=None)` - Execute agent requests
  - `get_help(topic=None)` - Get contextual help
- **Use Cases**: 
  - Coordinating complex multi-agent tasks
  - Intelligent request routing
  - Workflow orchestration

## Workflows

### Brownfield Full-Stack Workflow
Enhances existing full-stack applications through a structured process:

1. **Scope Classification** - Analyze and classify the enhancement scope
2. **Documentation Assessment** - Review existing documentation
3. **Project Analysis** - Analyze current project state
4. **PRD Creation** - Create Product Requirements Document
5. **Architecture Decisions** - Make architectural decisions
6. **Story Creation** - Create detailed user stories

```python
# Example usage
workflow = BrownfieldFullstackWorkflow()
state = await workflow.start_workflow({
    "project_name": "E-commerce Platform",
    "enhancement_type": "authentication_system"
})

# Execute steps sequentially
result = await workflow.execute_step(state.workflow_id, {
    "enhancement_description": "Add OAuth2 authentication with social login"
})
```

### Greenfield Full-Stack Workflow
Creates new full-stack applications from scratch:

1. **Requirements Gathering** - Collect and analyze initial requirements
2. **Architecture Design** - Design system architecture
3. **Technology Selection** - Choose appropriate technology stack
4. **Implementation Planning** - Create detailed implementation plan
5. **Development Coordination** - Coordinate development activities
6. **Quality Assurance** - Ensure quality throughout development

```python
# Example usage
workflow = GreenfieldFullstackWorkflow()
state = await workflow.start_workflow({
    "project_name": "New SaaS Platform",
    "project_type": "web_application"
})
```

## Configuration

Set up your environment variables in `.env` file:

```bash
# Required API keys
GOOGLE_AI_API_KEY=your_google_ai_key
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# Optional BMad-specific settings
BMAD_DEFAULT_MODEL=gemini-2.0-flash-exp
BMAD_OPENAI_MODEL=gpt-4o-mini
BMAD_AGENT_TIMEOUT=30
BMAD_MAX_CONCURRENT_AGENTS=5
BMAD_MAX_RETRIES=3
BMAD_WORKFLOW_PERSISTENCE=true
BMAD_WORKFLOW_TIMEOUT=1800
BMAD_LOG_LEVEL=INFO
BMAD_LOG_FILE=logs/bmad_agents.log
```

### Configuration Options

- **BMAD_DEFAULT_MODEL**: Default AI model for agents (default: gemini-2.0-flash-exp)
- **BMAD_AGENT_TIMEOUT**: Timeout for agent requests in seconds (default: 30)
- **BMAD_MAX_CONCURRENT_AGENTS**: Maximum concurrent agent executions (default: 5)
- **BMAD_MAX_RETRIES**: Number of retry attempts for failed requests (default: 3)
- **BMAD_WORKFLOW_PERSISTENCE**: Enable workflow state persistence (default: true)
- **BMAD_WORKFLOW_TIMEOUT**: Workflow timeout in seconds (default: 1800)
- **BMAD_LOG_LEVEL**: Logging level (DEBUG, INFO, WARNING, ERROR)

## Best Practices

### 1. Use the Orchestrator
Let the orchestrator route requests to appropriate agents rather than calling agents directly:

```python
# Good
response = await orchestrator.route_request(
    "I need to analyze requirements for user authentication"
)

# Less optimal
analyst = AnalystAgent()
result = await analyst.analyze_requirements("...")
```

### 2. Maintain Context
Pass relevant context between agent calls to maintain continuity:

```python
analysis = await orchestrator.execute_agent_request(
    "analyst", "Analyze requirements for user management"
)

architecture = await orchestrator.execute_agent_request(
    "architect", "Design architecture for user management",
    context={"analyst_result": analysis}
)
```

### 3. Handle Errors Gracefully
Implement proper error handling for agent interactions:

```python
try:
    result = await agent.execute_with_logging(request)
except AgentTimeoutError:
    print("Agent request timed out")
except AgentExecutionError as e:
    print(f"Agent execution failed: {e}")
```

### 4. Monitor Performance
Use built-in logging and monitoring to track agent performance:

```python
import logging
logging.getLogger('bmad').setLevel(logging.DEBUG)

# Performance metrics are automatically collected
from bmad_agents.base.monitoring import performance_monitor
summary = performance_monitor.get_metrics_summary()
```

### 5. Persist Workflow State
Use workflow persistence for long-running processes:

```python
# Workflow state is automatically persisted
state = await workflow.start_workflow(params)

# Later, resume from saved state
resumed_state = await workflow.state_manager.load_state(workflow_id)
```

## Advanced Usage

### Custom Agent Creation

Create custom agents by extending the base BMadAgent class:

```python
from bmad_agents.base.bmad_agent import BMadAgent
from pydantic import BaseModel

class CustomResponse(BaseModel):
    result: str
    confidence: float

class CustomAgent(BMadAgent):
    def __init__(self):
        super().__init__(
            role="custom",
            system_prompt="You are a custom agent...",
            response_model=CustomResponse
        )
    
    async def custom_method(self, input_data: str):
        return await self.execute_with_logging(
            f"Process this data: {input_data}"
        )
```

### Custom Workflow Creation

Create custom workflows by extending the base workflow class:

```python
from bmad_agents.workflows.base_workflow import BaseWorkflow

class CustomWorkflow(BaseWorkflow):
    def __init__(self):
        super().__init__("custom-workflow")
    
    async def _execute_custom_step(self, step_input, state):
        # Custom step implementation
        return {"result": "custom step completed"}
```

## Troubleshooting

### Common Issues

1. **API Key Errors**
   - Ensure all required API keys are set in your `.env` file
   - Check that API keys are valid and have sufficient quota
   - Verify the correct environment variable names

2. **Timeout Issues**
   - Increase `BMAD_AGENT_TIMEOUT` for complex requests
   - Check network connectivity
   - Consider breaking complex requests into smaller parts

3. **Memory Issues**
   - Reduce `BMAD_MAX_CONCURRENT_AGENTS` if experiencing memory problems
   - Monitor memory usage with built-in performance monitoring
   - Consider using workflow persistence for long-running processes

4. **Workflow State Issues**
   - Check workflow state files in `./workflow_states/` directory
   - Ensure `BMAD_WORKFLOW_PERSISTENCE` is enabled
   - Verify file system permissions for state directory

### Debugging

Enable debug logging to get detailed information:

```python
import logging

# Enable debug logging for all BMad components
logging.getLogger('bmad').setLevel(logging.DEBUG)

# Enable debug logging for specific components
logging.getLogger('bmad.orchestrator').setLevel(logging.DEBUG)
logging.getLogger('bmad.workflow').setLevel(logging.DEBUG)
```

### Performance Monitoring

Monitor system performance using built-in tools:

```python
from bmad_agents.base.monitoring import performance_monitor

# Get performance summary
summary = performance_monitor.get_metrics_summary()
print(f"Success rate: {summary['success_rate']:.2%}")
print(f"Average duration: {summary['average_duration']:.2f}s")

# Export detailed metrics
performance_monitor.export_metrics("performance_report.json")
```

## API Reference

For detailed API documentation, see the individual module documentation:

- [Base Classes](../bmad_agents/base/) - Core infrastructure classes
- [Agents](../bmad_agents/agents/) - Individual agent implementations
- [Workflows](../bmad_agents/workflows/) - Workflow orchestration
- [Examples](../bmad_agents/examples/) - Usage examples

## Contributing

To contribute to the BMad Pydantic AI Agents project:

1. Fork the repository
2. Create a feature branch
3. Implement your changes with tests
4. Run the test suite: `pytest tests/`
5. Submit a pull request

### Development Setup

```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
pytest tests/ -v

# Run integration tests
pytest tests/test_integration.py -v

# Check code quality
flake8 bmad_agents/
mypy bmad_agents/
```

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Support

For support and questions:

- Check the [troubleshooting section](#troubleshooting)
- Review the [examples](../bmad_agents/examples/)
- Open an issue on the project repository
- Consult the BMad Method documentation

---

*This guide covers the core functionality of the BMad Pydantic AI Agents system. For the most up-to-date information, please refer to the project repository and documentation.*