from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from ..base.bmad_agent import BMadAgent

class UserPersona(BaseModel):
    persona_id: str
    name: str
    age_range: str
    occupation: str
    goals: List[str]
    pain_points: List[str]
    behaviors: List[str]
    motivations: List[str]
    technology_comfort: str  # low, medium, high
    preferred_devices: List[str]
    context_of_use: str
    quote: str  # representative quote

class UserJourney(BaseModel):
    journey_id: str
    persona: str
    scenario: str
    touchpoints: List[str]
    actions: List[str]
    thoughts: List[str]
    emotions: List[str]
    pain_points: List[str]
    opportunities: List[str]
    success_metrics: List[str]
    journey_stages: List[str]  # awareness, consideration, purchase, onboarding, usage, advocacy

class Wireframe(BaseModel):
    wireframe_id: str
    screen_name: str
    device_type: str  # mobile, tablet, desktop
    layout_description: str
    components: List[str]
    navigation_elements: List[str]
    content_hierarchy: List[str]
    interaction_notes: List[str]
    responsive_considerations: List[str]
    accessibility_notes: List[str]

class DesignSystem(BaseModel):
    system_name: str
    color_palette: Dict[str, str]  # color_name -> hex_value
    typography: Dict[str, str]  # style_name -> font_specification
    spacing_scale: List[int]  # spacing values in pixels
    component_library: List[str]
    iconography_style: str
    imagery_guidelines: List[str]
    interaction_patterns: List[str]
    accessibility_standards: List[str]
    brand_guidelines: List[str]

class UsabilityTest(BaseModel):
    test_id: str
    test_type: str  # moderated, unmoderated, guerrilla, A/B
    objectives: List[str]
    target_users: List[str]
    test_scenarios: List[str]
    success_criteria: List[str]
    metrics_to_measure: List[str]
    test_environment: str
    duration: int  # minutes
    participant_requirements: List[str]

class UsabilityFindings(BaseModel):
    test_id: str
    participant_count: int
    completion_rate: float = Field(ge=0, le=1)
    average_task_time: float  # seconds
    error_rate: float = Field(ge=0, le=1)
    satisfaction_score: float = Field(ge=1, le=10)
    key_findings: List[str]
    usability_issues: List[str]
    user_feedback: List[str]
    recommendations: List[str]
    priority_fixes: List[str]

class AccessibilityAudit(BaseModel):
    audit_id: str
    audit_date: date = Field(default_factory=date.today)
    wcag_level: str  # A, AA, AAA
    tested_components: List[str]
    accessibility_issues: List[str]
    severity_levels: List[str]  # critical, high, medium, low
    compliance_score: float = Field(ge=0, le=100)
    keyboard_navigation: str  # pass, fail, partial
    screen_reader_compatibility: str  # pass, fail, partial
    color_contrast_results: List[str]
    remediation_plan: List[str]

class InteractionDesign(BaseModel):
    design_id: str
    interaction_type: str  # click, hover, swipe, gesture
    trigger_element: str
    interaction_flow: List[str]
    feedback_mechanisms: List[str]
    animation_specifications: List[str]
    micro_interactions: List[str]
    error_handling: List[str]
    loading_states: List[str]
    responsive_behavior: List[str]

class PrototypeSpec(BaseModel):
    prototype_id: str
    prototype_type: str  # low-fi, high-fi, interactive, clickable
    fidelity_level: str  # low, medium, high
    screens_included: List[str]
    user_flows: List[str]
    interactive_elements: List[str]
    testing_objectives: List[str]
    feedback_areas: List[str]
    iteration_notes: List[str]
    next_steps: List[str]

class DesignCritique(BaseModel):
    critique_id: str
    design_version: str
    critique_date: date = Field(default_factory=date.today)
    reviewers: List[str]
    design_strengths: List[str]
    areas_for_improvement: List[str]
    usability_concerns: List[str]
    accessibility_issues: List[str]
    brand_alignment: str  # excellent, good, needs_work, poor
    technical_feasibility: str  # high, medium, low
    action_items: List[str]

class UserResearch(BaseModel):
    research_id: str
    research_type: str  # interviews, surveys, observations, card_sorting
    research_objectives: List[str]
    target_participants: str
    methodology: str
    data_collection_methods: List[str]
    key_insights: List[str]
    user_needs: List[str]
    behavioral_patterns: List[str]
    recommendations: List[str]

class InformationArchitecture(BaseModel):
    ia_id: str
    site_structure: Dict[str, List[str]]  # parent -> children
    navigation_systems: List[str]
    labeling_conventions: List[str]
    search_strategy: str
    content_organization: str
    user_mental_models: List[str]
    findability_score: float = Field(ge=1, le=10)
    navigation_efficiency: str
    content_discoverability: List[str]

class UXAgent(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are an expert UX/UI Designer in the BMad Method framework.
        
        Your responsibilities include:
        - Conducting user research and creating user personas
        - Designing user journeys and experience flows
        - Creating wireframes, prototypes, and design systems
        - Conducting usability testing and accessibility audits
        - Designing interactions and micro-interactions
        - Ensuring design consistency and brand alignment
        - Collaborating with developers on implementation
        - Advocating for user-centered design principles
        
        Always prioritize user needs and accessibility.
        Focus on creating intuitive, inclusive, and delightful experiences.
        Base design decisions on research and user feedback.
        Maintain consistency with design systems and brand guidelines.
        """
        
        super().__init__(
            role="ux",
            system_prompt=system_prompt,
            response_model=UserPersona
        )
    
    async def create_user_personas(self, research_data: Dict[str, Any], target_audience: str) -> List[UserPersona]:
        """Create detailed user personas based on research data."""
        prompt = f"""
        Create comprehensive user personas based on the following research data:
        
        Research Data:
        {research_data}
        
        Target Audience:
        {target_audience}
        
        Create 3-5 distinct user personas that represent the primary user segments:
        
        For each persona, include:
        - Realistic demographic information and background
        - Clear goals and objectives when using the product
        - Specific pain points and frustrations
        - Behavioral patterns and preferences
        - Technology comfort level and device preferences
        - Context of use and environmental factors
        - A representative quote that captures their mindset
        
        Ensure personas are:
        - Based on real research data and insights
        - Distinct and representative of different user segments
        - Detailed enough to guide design decisions
        - Relatable and memorable for the team
        - Focused on goals and behaviors rather than demographics alone
        """
        
        class UserPersonaList(BaseModel):
            personas: List[UserPersona]
        
        temp_agent = BMadAgent(
            role="ux-personas",
            system_prompt=self.system_prompt,
            response_model=UserPersonaList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.personas
    
    async def design_user_journey(self, persona: str, scenario: str, product_context: Dict[str, Any]) -> UserJourney:
        """Design comprehensive user journey for specific persona and scenario."""
        prompt = f"""
        Design a detailed user journey for the following:
        
        Persona: {persona}
        Scenario: {scenario}
        Product Context: {product_context}
        
        Create a comprehensive user journey including:
        - All touchpoints and interactions with the product/service
        - User actions at each stage of the journey
        - User thoughts, emotions, and mental state
        - Pain points and friction areas
        - Opportunities for improvement and delight
        - Success metrics and desired outcomes
        - Journey stages from awareness to advocacy
        
        Focus on:
        - End-to-end experience across all channels
        - Emotional journey and user sentiment
        - Moments of truth and critical interactions
        - Opportunities to exceed expectations
        - Potential drop-off points and barriers
        """
        
        temp_agent = BMadAgent(
            role="ux-journey",
            system_prompt=self.system_prompt,
            response_model=UserJourney
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def create_wireframes(self, user_requirements: List[str], device_types: List[str], content_strategy: Dict[str, Any]) -> List[Wireframe]:
        """Create wireframes for key screens and user flows."""
        requirements_text = "\n".join([f"- {req}" for req in user_requirements])
        
        prompt = f"""
        Create wireframes for key screens based on the following requirements:
        
        User Requirements:
        {requirements_text}
        
        Target Device Types: {', '.join(device_types)}
        
        Content Strategy:
        {content_strategy}
        
        Create wireframes for the most important screens including:
        - Clear layout structure and component placement
        - Navigation elements and user flow connections
        - Content hierarchy and information architecture
        - Interactive elements and user actions
        - Responsive design considerations
        - Accessibility requirements and considerations
        
        Focus on:
        - User task completion and goal achievement
        - Information hierarchy and visual flow
        - Intuitive navigation and wayfinding
        - Content prioritization and organization
        - Mobile-first responsive design approach
        """
        
        class WireframeList(BaseModel):
            wireframes: List[Wireframe]
        
        temp_agent = BMadAgent(
            role="ux-wireframes",
            system_prompt=self.system_prompt,
            response_model=WireframeList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.wireframes
    
    async def develop_design_system(self, brand_guidelines: Dict[str, Any], product_requirements: List[str]) -> DesignSystem:
        """Develop comprehensive design system for consistent UI."""
        prompt = f"""
        Develop a comprehensive design system based on:
        
        Brand Guidelines:
        {brand_guidelines}
        
        Product Requirements:
        {chr(10).join(product_requirements)}
        
        Create a design system including:
        - Color palette with primary, secondary, and semantic colors
        - Typography scale with font families, sizes, and weights
        - Spacing system with consistent scale and rhythm
        - Component library with reusable UI elements
        - Iconography style and usage guidelines
        - Imagery guidelines and visual style
        - Interaction patterns and micro-interactions
        - Accessibility standards and compliance requirements
        - Brand alignment and visual identity integration
        
        Ensure the design system:
        - Supports scalability and consistency
        - Meets accessibility standards (WCAG 2.1 AA)
        - Aligns with brand identity and values
        - Facilitates efficient design and development
        - Provides clear usage guidelines and examples
        """
        
        temp_agent = BMadAgent(
            role="ux-design-system",
            system_prompt=self.system_prompt,
            response_model=DesignSystem
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def plan_usability_test(self, test_objectives: List[str], target_users: str, prototype_details: Dict[str, Any]) -> UsabilityTest:
        """Plan comprehensive usability testing session."""
        prompt = f"""
        Plan a usability testing session with the following parameters:
        
        Test Objectives:
        {chr(10).join(test_objectives)}
        
        Target Users: {target_users}
        
        Prototype Details:
        {prototype_details}
        
        Create a comprehensive test plan including:
        - Appropriate test methodology and approach
        - Clear objectives and success criteria
        - Realistic test scenarios and tasks
        - Relevant metrics and measurement approach
        - Participant requirements and recruitment criteria
        - Test environment and setup requirements
        - Session duration and timeline
        
        Focus on:
        - Actionable insights and findings
        - User behavior observation and analysis
        - Task completion and user satisfaction
        - Identifying usability issues and improvements
        - Validating design decisions and assumptions
        """
        
        temp_agent = BMadAgent(
            role="ux-usability-test",
            system_prompt=self.system_prompt,
            response_model=UsabilityTest
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def analyze_usability_results(self, test_data: Dict[str, Any], user_feedback: List[str]) -> UsabilityFindings:
        """Analyze usability test results and provide recommendations."""
        feedback_summary = "\n".join([f"- {feedback}" for feedback in user_feedback])
        
        prompt = f"""
        Analyze usability test results and provide comprehensive findings:
        
        Test Data:
        {test_data}
        
        User Feedback:
        {feedback_summary}
        
        Provide detailed analysis including:
        - Quantitative metrics (completion rate, task time, error rate)
        - Qualitative insights from user feedback and observations
        - Key usability issues and their impact on user experience
        - User satisfaction scores and sentiment analysis
        - Prioritized recommendations for improvement
        - Critical fixes that should be addressed immediately
        
        Focus on:
        - Actionable insights that inform design decisions
        - Root cause analysis of usability issues
        - Impact assessment on user goals and business objectives
        - Specific, measurable improvement recommendations
        - Validation or invalidation of design hypotheses
        """
        
        temp_agent = BMadAgent(
            role="ux-usability-analysis",
            system_prompt=self.system_prompt,
            response_model=UsabilityFindings
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def conduct_accessibility_audit(self, design_components: List[str], wcag_level: str) -> AccessibilityAudit:
        """Conduct comprehensive accessibility audit of design components."""
        components_text = "\n".join([f"- {component}" for component in design_components])
        
        prompt = f"""
        Conduct a comprehensive accessibility audit for the following components:
        
        Design Components:
        {components_text}
        
        WCAG Compliance Level: {wcag_level}
        
        Perform accessibility audit covering:
        - WCAG 2.1 compliance assessment at specified level
        - Keyboard navigation and focus management
        - Screen reader compatibility and semantic markup
        - Color contrast ratios and visual accessibility
        - Alternative text and media accessibility
        - Form accessibility and error handling
        - Interactive element accessibility
        - Mobile accessibility considerations
        
        Provide:
        - Detailed findings with severity classifications
        - Compliance score and gap analysis
        - Specific remediation recommendations
        - Implementation guidance for developers
        - Testing procedures for ongoing compliance
        """
        
        temp_agent = BMadAgent(
            role="ux-accessibility",
            system_prompt=self.system_prompt,
            response_model=AccessibilityAudit
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def design_interactions(self, user_flows: List[str], interface_elements: List[str]) -> List[InteractionDesign]:
        """Design detailed interactions and micro-interactions."""
        flows_text = "\n".join([f"- {flow}" for flow in user_flows])
        elements_text = "\n".join([f"- {element}" for element in interface_elements])
        
        prompt = f"""
        Design detailed interactions for the following user flows and interface elements:
        
        User Flows:
        {flows_text}
        
        Interface Elements:
        {elements_text}
        
        Create interaction designs including:
        - Trigger mechanisms and user actions
        - Interaction flow and state transitions
        - Visual and haptic feedback mechanisms
        - Animation specifications and timing
        - Micro-interactions and delightful moments
        - Error states and recovery mechanisms
        - Loading states and progress indicators
        - Responsive behavior across devices
        
        Focus on:
        - Intuitive and discoverable interactions
        - Consistent interaction patterns
        - Appropriate feedback and confirmation
        - Smooth transitions and animations
        - Accessibility of interactive elements
        """
        
        class InteractionDesignList(BaseModel):
            interactions: List[InteractionDesign]
        
        temp_agent = BMadAgent(
            role="ux-interactions",
            system_prompt=self.system_prompt,
            response_model=InteractionDesignList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.interactions
    
    async def create_prototype_spec(self, design_objectives: List[str], target_fidelity: str, testing_goals: List[str]) -> PrototypeSpec:
        """Create specifications for prototype development."""
        prompt = f"""
        Create prototype specifications based on the following requirements:
        
        Design Objectives:
        {chr(10).join(design_objectives)}
        
        Target Fidelity Level: {target_fidelity}
        
        Testing Goals:
        {chr(10).join(testing_goals)}
        
        Create prototype specifications including:
        - Appropriate prototype type and fidelity level
        - Key screens and user flows to include
        - Interactive elements and functionality
        - Testing objectives and validation goals
        - Areas for user feedback and iteration
        - Technical requirements and constraints
        - Timeline and resource considerations
        
        Focus on:
        - Balancing fidelity with development speed
        - Including critical user paths and interactions
        - Enabling effective user testing and feedback
        - Supporting design validation and iteration
        - Facilitating stakeholder communication
        """
        
        temp_agent = BMadAgent(
            role="ux-prototype",
            system_prompt=self.system_prompt,
            response_model=PrototypeSpec
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def facilitate_design_critique(self, design_version: str, stakeholders: List[str], critique_focus: List[str]) -> DesignCritique:
        """Facilitate design critique session and document feedback."""
        focus_areas = "\n".join([f"- {area}" for area in critique_focus])
        
        prompt = f"""
        Facilitate a design critique session for the following:
        
        Design Version: {design_version}
        Stakeholders: {', '.join(stakeholders)}
        
        Critique Focus Areas:
        {focus_areas}
        
        Structure the critique to cover:
        - Design strengths and successful elements
        - Areas needing improvement or refinement
        - Usability concerns and user experience issues
        - Accessibility compliance and inclusive design
        - Brand alignment and visual consistency
        - Technical feasibility and implementation considerations
        - Actionable next steps and priorities
        
        Ensure the critique:
        - Focuses on user needs and business objectives
        - Provides constructive and specific feedback
        - Balances positive reinforcement with improvement areas
        - Results in clear action items and decisions
        - Maintains collaborative and respectful dialogue
        """
        
        temp_agent = BMadAgent(
            role="ux-critique",
            system_prompt=self.system_prompt,
            response_model=DesignCritique
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def conduct_user_research(self, research_objectives: List[str], methodology: str, target_participants: str) -> UserResearch:
        """Plan and conduct user research study."""
        objectives_text = "\n".join([f"- {obj}" for obj in research_objectives])
        
        prompt = f"""
        Plan and conduct user research study with the following parameters:
        
        Research Objectives:
        {objectives_text}
        
        Methodology: {methodology}
        Target Participants: {target_participants}
        
        Design research study including:
        - Appropriate research methodology and approach
        - Data collection methods and tools
        - Participant recruitment and screening criteria
        - Research protocol and question framework
        - Analysis approach and insight extraction
        - Key findings and user insights
        - Behavioral patterns and mental models
        - Actionable recommendations for design
        
        Focus on:
        - Uncovering user needs and pain points
        - Understanding user behavior and motivations
        - Validating or challenging design assumptions
        - Informing design decisions with evidence
        - Building empathy and user understanding
        """
        
        temp_agent = BMadAgent(
            role="ux-research",
            system_prompt=self.system_prompt,
            response_model=UserResearch
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def design_information_architecture(self, content_inventory: List[str], user_needs: List[str], business_goals: List[str]) -> InformationArchitecture:
        """Design information architecture and navigation structure."""
        content_text = "\n".join([f"- {item}" for item in content_inventory])
        needs_text = "\n".join([f"- {need}" for need in user_needs])
        goals_text = "\n".join([f"- {goal}" for goal in business_goals])
        
        prompt = f"""
        Design information architecture based on the following inputs:
        
        Content Inventory:
        {content_text}
        
        User Needs:
        {needs_text}
        
        Business Goals:
        {goals_text}
        
        Create information architecture including:
        - Logical site structure and content hierarchy
        - Navigation systems and wayfinding mechanisms
        - Content labeling and categorization conventions
        - Search strategy and findability optimization
        - Content organization principles and rationale
        - User mental models and expectation alignment
        - Navigation efficiency and task completion support
        - Content discoverability and exploration paths
        
        Focus on:
        - User task completion and goal achievement
        - Intuitive content organization and findability
        - Scalable and maintainable structure
        - Cross-platform consistency and coherence
        - Search engine optimization and discoverability
        """
        
        temp_agent = BMadAgent(
            role="ux-ia",
            system_prompt=self.system_prompt,
            response_model=InformationArchitecture
        )
        
        return await temp_agent.execute_with_logging(prompt)