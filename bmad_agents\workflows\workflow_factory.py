from typing import Dict, Type, Optional
from .base_workflow import BaseWorkflow
from .brownfield_fullstack import BrownfieldFullstackWorkflow
from .greenfield_fullstack import GreenfieldFullstackWorkflow

class WorkflowFactory:
    """Factory class for creating workflow instances."""
    
    _workflow_registry: Dict[str, Type[BaseWorkflow]] = {
        "brownfield-fullstack": BrownfieldFullstackWorkflow,
        "greenfield-fullstack": GreenfieldFullstackWorkflow,
    }
    
    @classmethod
    def create_workflow(cls, workflow_type: str) -> BaseWorkflow:
        """Create a workflow instance by type.
        
        Args:
            workflow_type: The type of workflow to create
            
        Returns:
            BaseWorkflow: An instance of the requested workflow
            
        Raises:
            ValueError: If workflow type is not registered
        """
        if workflow_type not in cls._workflow_registry:
            available_types = list(cls._workflow_registry.keys())
            raise ValueError(
                f"Unknown workflow type: {workflow_type}. "
                f"Available types: {available_types}"
            )
        
        workflow_class = cls._workflow_registry[workflow_type]
        return workflow_class()
    
    @classmethod
    def register_workflow(cls, workflow_type: str, workflow_class: Type[BaseWorkflow]) -> None:
        """Register a new workflow type.
        
        Args:
            workflow_type: The type identifier for the workflow
            workflow_class: The workflow class to register
        """
        if not issubclass(workflow_class, BaseWorkflow):
            raise TypeError("Workflow class must inherit from BaseWorkflow")
        
        cls._workflow_registry[workflow_type] = workflow_class
    
    @classmethod
    def get_available_workflows(cls) -> Dict[str, Type[BaseWorkflow]]:
        """Get all available workflow types.
        
        Returns:
            Dict mapping workflow type names to their classes
        """
        return cls._workflow_registry.copy()
    
    @classmethod
    def is_workflow_available(cls, workflow_type: str) -> bool:
        """Check if a workflow type is available.
        
        Args:
            workflow_type: The workflow type to check
            
        Returns:
            bool: True if the workflow type is available
        """
        return workflow_type in cls._workflow_registry
    
    @classmethod
    def get_workflow_info(cls, workflow_type: str) -> Optional[Dict[str, str]]:
        """Get information about a workflow type.
        
        Args:
            workflow_type: The workflow type to get info for
            
        Returns:
            Dict with workflow information or None if not found
        """
        if workflow_type not in cls._workflow_registry:
            return None
        
        workflow_class = cls._workflow_registry[workflow_type]
        return {
            "type": workflow_type,
            "class_name": workflow_class.__name__,
            "description": workflow_class.__doc__ or "No description available",
            "module": workflow_class.__module__
        }