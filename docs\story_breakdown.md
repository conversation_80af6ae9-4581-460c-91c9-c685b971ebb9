# BMad Pydantic AI Agents - Story Implementation Breakdown

## Epic 1: BMad Pydantic AI Agents Implementation

This document provides detailed implementation guidance for each story in the BMad Pydantic AI Agents PRD.

---

## Story 1.1: Base BMad Agent Infrastructure

### Implementation Tasks

#### Task 1.1.1: Create Base BMad Agent Class
**File**: `bmad_agents/base/bmad_agent.py`

```python
from pydantic_ai import Agent
from pydantic import BaseModel
from typing import Type, List, Optional, Dict, Any
from datetime import datetime
import logging
import asyncio

class BMadAgent(Agent):
    """
    Base class for all BMad Method agents.
    Extends Pydantic AI Agent with BMad-specific capabilities.
    """
    
    def __init__(
        self,
        role: str,
        model: str = 'gemini-2.0-flash-exp',
        system_prompt: str = None,
        response_model: Type[BaseModel] = None,
        tools: List = None,
        **kwargs
    ):
        self.role = role
        self.created_at = datetime.now()
        self.logger = logging.getLogger(f"bmad.{role}")
        
        super().__init__(
            model=model,
            system_prompt=system_prompt or self._get_default_system_prompt(),
            result_type=response_model,
            tools=tools,
            **kwargs
        )
    
    def _get_default_system_prompt(self) -> str:
        return f"You are a {self.role} agent in the BMad Method framework."
    
    async def execute_with_logging(self, request: str, context: Dict[str, Any] = None):
        """Execute agent request with comprehensive logging."""
        self.logger.info(f"Executing request: {request[:100]}...")
        start_time = datetime.now()
        
        try:
            result = await self.run(request, message_history=context.get('history', []) if context else [])
            duration = (datetime.now() - start_time).total_seconds()
            self.logger.info(f"Request completed in {duration:.2f}s")
            return result
        except Exception as e:
            self.logger.error(f"Request failed: {str(e)}")
            raise
```

#### Task 1.1.2: Create Shared Communication Models
**File**: `bmad_agents/base/models.py`

```python
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum

class MessageType(str, Enum):
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    ERROR = "error"

class ResponseStatus(str, Enum):
    SUCCESS = "success"
    ERROR = "error"
    PENDING = "pending"
    TIMEOUT = "timeout"

class AgentMessage(BaseModel):
    from_agent: str
    to_agent: str
    message_type: MessageType
    content: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)
    correlation_id: str
    metadata: Dict[str, Any] = Field(default_factory=dict)

class AgentRequest(BaseModel):
    request_id: str
    agent_role: str
    action: str
    parameters: Dict[str, Any]
    context: Dict[str, Any] = Field(default_factory=dict)
    timeout: Optional[int] = 30

class AgentResponse(BaseModel):
    request_id: str
    agent_role: str
    status: ResponseStatus
    result: Dict[str, Any]
    metadata: Dict[str, Any] = Field(default_factory=dict)
    execution_time: Optional[float] = None
    error_message: Optional[str] = None

class WorkflowState(BaseModel):
    workflow_id: str
    workflow_type: str
    current_step: str
    completed_steps: List[str] = Field(default_factory=list)
    agent_contexts: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    shared_context: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    status: str = "active"

class WorkflowStep(BaseModel):
    step_id: str
    step_name: str
    required_agents: List[str]
    input_requirements: Dict[str, Any]
    output_specifications: Dict[str, Any]
    dependencies: List[str] = Field(default_factory=list)
    timeout: int = 300  # 5 minutes default
```

#### Task 1.1.3: Create State Manager
**File**: `bmad_agents/base/state_manager.py`

```python
import json
import os
from typing import Dict, Optional
from datetime import datetime
from .models import WorkflowState
import aiofiles

class StateManager:
    """
    Manages workflow state persistence and retrieval.
    """
    
    def __init__(self, state_dir: str = "./workflow_states"):
        self.state_dir = state_dir
        os.makedirs(state_dir, exist_ok=True)
    
    def _get_state_file(self, workflow_id: str) -> str:
        return os.path.join(self.state_dir, f"{workflow_id}.json")
    
    async def save_state(self, workflow_id: str, state: WorkflowState) -> None:
        """Save workflow state to disk."""
        state.updated_at = datetime.now()
        state_file = self._get_state_file(workflow_id)
        
        async with aiofiles.open(state_file, 'w') as f:
            await f.write(state.model_dump_json(indent=2))
    
    async def load_state(self, workflow_id: str) -> Optional[WorkflowState]:
        """Load workflow state from disk."""
        state_file = self._get_state_file(workflow_id)
        
        if not os.path.exists(state_file):
            return None
        
        async with aiofiles.open(state_file, 'r') as f:
            content = await f.read()
            return WorkflowState.model_validate_json(content)
    
    async def delete_state(self, workflow_id: str) -> bool:
        """Delete workflow state file."""
        state_file = self._get_state_file(workflow_id)
        
        if os.path.exists(state_file):
            os.remove(state_file)
            return True
        return False
    
    async def list_workflows(self) -> List[str]:
        """List all workflow IDs with saved states."""
        workflows = []
        for filename in os.listdir(self.state_dir):
            if filename.endswith('.json'):
                workflows.append(filename[:-5])  # Remove .json extension
        return workflows
```

#### Task 1.1.4: Create Configuration Manager
**File**: `bmad_agents/base/config.py`

```python
import os
from typing import Dict, Any, Optional
from pydantic import BaseModel
from dotenv import load_dotenv

load_dotenv()

class BMadConfig(BaseModel):
    # AI Model Configuration
    default_model: str = "gemini-2.0-flash-exp"
    openai_model: str = "gpt-4o-mini"
    
    # API Keys
    google_ai_api_key: Optional[str] = None
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    
    # Agent Configuration
    agent_timeout: int = 30
    max_concurrent_agents: int = 5
    max_retries: int = 3
    
    # Workflow Configuration
    workflow_persistence: bool = True
    workflow_timeout: int = 1800  # 30 minutes
    
    # Logging Configuration
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    @classmethod
    def from_env(cls) -> 'BMadConfig':
        return cls(
            default_model=os.getenv('BMAD_DEFAULT_MODEL', 'gemini-2.0-flash-exp'),
            openai_model=os.getenv('BMAD_OPENAI_MODEL', 'gpt-4o-mini'),
            google_ai_api_key=os.getenv('GOOGLE_AI_API_KEY'),
            openai_api_key=os.getenv('OPENAI_API_KEY'),
            anthropic_api_key=os.getenv('ANTHROPIC_API_KEY'),
            agent_timeout=int(os.getenv('BMAD_AGENT_TIMEOUT', '30')),
            max_concurrent_agents=int(os.getenv('BMAD_MAX_CONCURRENT_AGENTS', '5')),
            max_retries=int(os.getenv('BMAD_MAX_RETRIES', '3')),
            workflow_persistence=os.getenv('BMAD_WORKFLOW_PERSISTENCE', 'true').lower() == 'true',
            workflow_timeout=int(os.getenv('BMAD_WORKFLOW_TIMEOUT', '1800')),
            log_level=os.getenv('BMAD_LOG_LEVEL', 'INFO'),
            log_file=os.getenv('BMAD_LOG_FILE')
        )

# Global configuration instance
config = BMadConfig.from_env()
```

#### Task 1.1.5: Create Logging Infrastructure
**File**: `bmad_agents/base/logging_config.py`

```python
import logging
import sys
from typing import Optional
from .config import config

def setup_logging(log_level: str = None, log_file: Optional[str] = None) -> None:
    """
    Configure logging for BMad agents.
    """
    log_level = log_level or config.log_level
    log_file = log_file or config.log_file
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Configure root logger
    root_logger = logging.getLogger('bmad')
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler (if specified)
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Prevent duplicate logs
    root_logger.propagate = False

# Initialize logging
setup_logging()
```

### Testing Tasks

#### Task 1.1.6: Create Base Infrastructure Tests
**File**: `tests/test_base_infrastructure.py`

```python
import pytest
import asyncio
from bmad_agents.base.bmad_agent import BMadAgent
from bmad_agents.base.models import WorkflowState, AgentMessage, MessageType
from bmad_agents.base.state_manager import StateManager
from bmad_agents.base.config import BMadConfig

class TestBMadAgent:
    @pytest.mark.asyncio
    async def test_agent_initialization(self):
        agent = BMadAgent(role="test", model="gemini-2.0-flash-exp")
        assert agent.role == "test"
        assert agent.created_at is not None
    
    @pytest.mark.asyncio
    async def test_agent_logging(self):
        agent = BMadAgent(role="test")
        # Test that logging is properly configured
        assert agent.logger.name == "bmad.test"

class TestStateManager:
    @pytest.mark.asyncio
    async def test_save_and_load_state(self):
        state_manager = StateManager(state_dir="./test_states")
        
        # Create test state
        state = WorkflowState(
            workflow_id="test-123",
            workflow_type="test",
            current_step="step1"
        )
        
        # Save and load
        await state_manager.save_state("test-123", state)
        loaded_state = await state_manager.load_state("test-123")
        
        assert loaded_state.workflow_id == "test-123"
        assert loaded_state.workflow_type == "test"
        
        # Cleanup
        await state_manager.delete_state("test-123")

class TestConfig:
    def test_config_from_env(self):
        config = BMadConfig.from_env()
        assert config.default_model is not None
        assert config.agent_timeout > 0
```

---

## Story 1.2: Core BMad Agents Implementation

### Implementation Tasks

#### Task 1.2.1: Create Analyst Agent
**File**: `bmad_agents/agents/analyst.py`

```python
from pydantic import BaseModel, Field
from typing import List, Dict, Any
from ..base.bmad_agent import BMadAgent

class RequirementAnalysis(BaseModel):
    summary: str = Field(description="High-level summary of requirements")
    functional_requirements: List[str] = Field(description="List of functional requirements")
    non_functional_requirements: List[str] = Field(description="List of non-functional requirements")
    assumptions: List[str] = Field(description="Key assumptions made")
    risks: List[str] = Field(description="Identified risks")
    recommendations: List[str] = Field(description="Recommendations for implementation")

class UserStory(BaseModel):
    title: str
    description: str
    acceptance_criteria: List[str]
    priority: str  # High, Medium, Low
    effort_estimate: str  # Small, Medium, Large
    dependencies: List[str] = Field(default_factory=list)

class AnalystAgent(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are an expert Business Analyst in the BMad Method framework.
        
        Your responsibilities include:
        - Analyzing business requirements and user needs
        - Creating detailed user stories with acceptance criteria
        - Identifying risks and assumptions
        - Providing recommendations for implementation approach
        
        Always provide structured, actionable analysis that can guide development decisions.
        Focus on clarity, completeness, and practical implementation considerations.
        """
        
        super().__init__(
            role="analyst",
            system_prompt=system_prompt,
            response_model=RequirementAnalysis
        )
    
    async def analyze_requirements(self, requirements: str, context: Dict[str, Any] = None) -> RequirementAnalysis:
        """Analyze business requirements and provide structured analysis."""
        prompt = f"""
        Please analyze the following requirements and provide a comprehensive analysis:
        
        Requirements:
        {requirements}
        
        {f"Additional Context: {context}" if context else ""}
        
        Provide a detailed analysis including functional and non-functional requirements,
        assumptions, risks, and implementation recommendations.
        """
        
        return await self.execute_with_logging(prompt, context)
    
    async def create_user_stories(self, analysis: RequirementAnalysis, additional_context: str = "") -> List[UserStory]:
        """Create user stories based on requirements analysis."""
        prompt = f"""
        Based on the following requirements analysis, create detailed user stories:
        
        Analysis Summary: {analysis.summary}
        Functional Requirements: {', '.join(analysis.functional_requirements)}
        
        {f"Additional Context: {additional_context}" if additional_context else ""}
        
        Create comprehensive user stories with:
        - Clear titles and descriptions
        - Specific acceptance criteria
        - Priority levels (High/Medium/Low)
        - Effort estimates (Small/Medium/Large)
        - Dependencies between stories
        
        Return as a list of UserStory objects.
        """
        
        # Note: This would need custom handling since we want a List[UserStory]
        # For now, we'll use a wrapper model
        class UserStoryList(BaseModel):
            stories: List[UserStory]
        
        temp_agent = BMadAgent(
            role="analyst-stories",
            system_prompt=self.system_prompt,
            response_model=UserStoryList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.stories
```

#### Task 1.2.2: Create Architect Agent
**File**: `bmad_agents/agents/architect.py`

```python
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from ..base.bmad_agent import BMadAgent

class TechnicalComponent(BaseModel):
    name: str
    description: str
    technology: str
    responsibilities: List[str]
    interfaces: List[str] = Field(default_factory=list)
    dependencies: List[str] = Field(default_factory=list)

class ArchitecturalDecision(BaseModel):
    decision: str
    rationale: str
    alternatives_considered: List[str]
    implications: List[str]
    risks: List[str] = Field(default_factory=list)

class SystemArchitecture(BaseModel):
    overview: str
    components: List[TechnicalComponent]
    architectural_decisions: List[ArchitecturalDecision]
    technology_stack: Dict[str, str]
    deployment_considerations: List[str]
    scalability_considerations: List[str]
    security_considerations: List[str]

class ArchitectAgent(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are an expert Software Architect in the BMad Method framework.
        
        Your responsibilities include:
        - Designing system architecture and technical solutions
        - Making technology stack decisions
        - Defining component interfaces and interactions
        - Considering scalability, security, and maintainability
        - Documenting architectural decisions and rationale
        
        Always provide well-reasoned architectural decisions with clear rationale.
        Consider both current requirements and future scalability needs.
        Focus on maintainable, testable, and robust solutions.
        """
        
        super().__init__(
            role="architect",
            system_prompt=system_prompt,
            response_model=SystemArchitecture
        )
    
    async def design_architecture(self, requirements: str, constraints: Dict[str, Any] = None) -> SystemArchitecture:
        """Design system architecture based on requirements."""
        prompt = f"""
        Design a comprehensive system architecture for the following requirements:
        
        Requirements:
        {requirements}
        
        {f"Constraints: {constraints}" if constraints else ""}
        
        Provide a detailed architecture including:
        - System overview and high-level design
        - Key components with responsibilities and interfaces
        - Architectural decisions with rationale
        - Technology stack recommendations
        - Deployment, scalability, and security considerations
        """
        
        return await self.execute_with_logging(prompt, {'constraints': constraints})
    
    async def review_architecture(self, architecture: SystemArchitecture, criteria: List[str]) -> Dict[str, Any]:
        """Review existing architecture against specific criteria."""
        prompt = f"""
        Review the following system architecture against these criteria:
        
        Architecture Overview: {architecture.overview}
        Components: {len(architecture.components)} components defined
        Technology Stack: {architecture.technology_stack}
        
        Review Criteria:
        {', '.join(criteria)}
        
        Provide a detailed review with:
        - Strengths of the current architecture
        - Areas for improvement
        - Specific recommendations
        - Risk assessment
        """
        
        class ArchitectureReview(BaseModel):
            strengths: List[str]
            improvements: List[str]
            recommendations: List[str]
            risk_assessment: List[str]
            overall_score: int = Field(ge=1, le=10)
        
        temp_agent = BMadAgent(
            role="architect-review",
            system_prompt=self.system_prompt,
            response_model=ArchitectureReview
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.model_dump()
```

#### Task 1.2.3-1.2.8: Create Remaining Agents
**Files**: `bmad_agents/agents/pm.py`, `po.py`, `sm.py`, `dev.py`, `qa.py`, `ux_expert.py`

*[Similar structure for each agent with role-specific models and methods]*

### Testing Tasks

#### Task 1.2.9: Create Agent Tests
**File**: `tests/test_agents.py`

```python
import pytest
from bmad_agents.agents.analyst import AnalystAgent, RequirementAnalysis
from bmad_agents.agents.architect import ArchitectAgent, SystemArchitecture

class TestAnalystAgent:
    @pytest.mark.asyncio
    async def test_analyze_requirements(self):
        agent = AnalystAgent()
        requirements = "Create a web application for task management"
        
        # Mock the actual AI call for testing
        # In real tests, you might use a test model or mock responses
        result = await agent.analyze_requirements(requirements)
        
        assert isinstance(result, RequirementAnalysis)
        assert result.summary is not None
        assert len(result.functional_requirements) > 0

class TestArchitectAgent:
    @pytest.mark.asyncio
    async def test_design_architecture(self):
        agent = ArchitectAgent()
        requirements = "Design architecture for a scalable web application"
        
        result = await agent.design_architecture(requirements)
        
        assert isinstance(result, SystemArchitecture)
        assert result.overview is not None
        assert len(result.components) > 0
```

---

## Story 1.3: BMad Orchestrator Agent

### Implementation Tasks

#### Task 1.3.1: Create BMad Orchestrator
**File**: `bmad_agents/agents/orchestrator.py`

```python
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Union
from ..base.bmad_agent import BMadAgent
from ..base.models import AgentRequest, AgentResponse, WorkflowState
from ..base.state_manager import StateManager
from .analyst import AnalystAgent
from .architect import ArchitectAgent
# Import other agents...

class OrchestrationResponse(BaseModel):
    action: str
    target_agent: Optional[str] = None
    workflow_type: Optional[str] = None
    message: str
    next_steps: List[str] = Field(default_factory=list)
    context_updates: Dict[str, Any] = Field(default_factory=dict)

class BMadOrchestrator(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are the BMad Orchestrator, the central coordination agent in the BMad Method framework.
        
        Your responsibilities include:
        - Routing user requests to appropriate specialized agents
        - Coordinating multi-agent workflows
        - Managing workflow state and context
        - Providing guidance on BMad Method usage
        - Facilitating smooth handoffs between agents
        
        Available agents and their roles:
        - Analyst: Requirements analysis, user story creation
        - Architect: System design, technical decisions
        - PM: Project planning, resource management
        - PO: Product vision, backlog management
        - SM: Process facilitation, team coordination
        - Dev: Code implementation, technical execution
        - QA: Quality assurance, testing
        - UX Expert: User experience design, usability
        
        Available workflows:
        - brownfield-fullstack: Enhance existing full-stack applications
        - greenfield-fullstack: Create new full-stack applications
        
        Always provide clear guidance and coordinate effectively between agents.
        """
        
        super().__init__(
            role="orchestrator",
            system_prompt=system_prompt,
            response_model=OrchestrationResponse
        )
        
        # Initialize agents
        self.agents = {
            'analyst': AnalystAgent(),
            'architect': ArchitectAgent(),
            # Initialize other agents...
        }
        
        self.state_manager = StateManager()
        self.active_workflows: Dict[str, WorkflowState] = {}
    
    async def route_request(self, request: str, context: Dict[str, Any] = None) -> OrchestrationResponse:
        """Route user request to appropriate agent or workflow."""
        prompt = f"""
        Analyze the following user request and determine the appropriate action:
        
        User Request: {request}
        
        {f"Current Context: {context}" if context else ""}
        
        Determine:
        1. What type of request this is (single agent task, workflow initiation, help request, etc.)
        2. Which agent should handle it (if single agent task)
        3. Which workflow should be initiated (if workflow request)
        4. What message to provide to the user
        5. What the next steps should be
        
        Provide clear guidance and coordination.
        """
        
        return await self.execute_with_logging(prompt, context)
    
    async def execute_agent_request(self, agent_name: str, request: str, context: Dict[str, Any] = None) -> Any:
        """Execute request on specific agent."""
        if agent_name not in self.agents:
            raise ValueError(f"Unknown agent: {agent_name}")
        
        agent = self.agents[agent_name]
        return await agent.execute_with_logging(request, context)
    
    async def get_help(self, topic: str = None) -> str:
        """Provide help information about BMad Method and available agents."""
        if topic:
            prompt = f"Provide detailed help information about: {topic}"
        else:
            prompt = "Provide an overview of the BMad Method, available agents, and how to use them."
        
        class HelpResponse(BaseModel):
            help_content: str
        
        temp_agent = BMadAgent(
            role="help",
            system_prompt=self.system_prompt,
            response_model=HelpResponse
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.help_content
```

---

## Story 1.4: Workflow Engine Implementation

### Implementation Tasks

#### Task 1.4.1: Create Base Workflow Engine
**File**: `bmad_agents/workflows/base_workflow.py`

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from ..base.models import WorkflowState, WorkflowStep
from ..base.state_manager import StateManager
import uuid
from datetime import datetime

class BaseWorkflow(ABC):
    def __init__(self, workflow_type: str):
        self.workflow_type = workflow_type
        self.state_manager = StateManager()
        self.steps = self._define_steps()
    
    @abstractmethod
    def _define_steps(self) -> List[WorkflowStep]:
        """Define the workflow steps."""
        pass
    
    async def start_workflow(self, context: Dict[str, Any]) -> WorkflowState:
        """Start a new workflow instance."""
        workflow_id = str(uuid.uuid4())
        
        state = WorkflowState(
            workflow_id=workflow_id,
            workflow_type=self.workflow_type,
            current_step=self.steps[0].step_id,
            shared_context=context
        )
        
        await self.state_manager.save_state(workflow_id, state)
        return state
    
    async def execute_step(self, workflow_id: str, step_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the current workflow step."""
        state = await self.state_manager.load_state(workflow_id)
        if not state:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        current_step = self._get_step(state.current_step)
        if not current_step:
            raise ValueError(f"Step {state.current_step} not found")
        
        # Execute step logic
        result = await self._execute_step_logic(current_step, step_input, state)
        
        # Update state
        state.completed_steps.append(state.current_step)
        next_step = self._get_next_step(state.current_step)
        
        if next_step:
            state.current_step = next_step.step_id
        else:
            state.status = "completed"
        
        state.shared_context.update(result.get('context_updates', {}))
        await self.state_manager.save_state(workflow_id, state)
        
        return result
    
    @abstractmethod
    async def _execute_step_logic(self, step: WorkflowStep, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute the logic for a specific step."""
        pass
    
    def _get_step(self, step_id: str) -> Optional[WorkflowStep]:
        """Get step by ID."""
        return next((step for step in self.steps if step.step_id == step_id), None)
    
    def _get_next_step(self, current_step_id: str) -> Optional[WorkflowStep]:
        """Get the next step in the workflow."""
        current_index = next((i for i, step in enumerate(self.steps) if step.step_id == current_step_id), -1)
        if current_index >= 0 and current_index < len(self.steps) - 1:
            return self.steps[current_index + 1]
        return None
```

#### Task 1.4.2: Create Brownfield Workflow
**File**: `bmad_agents/workflows/brownfield_fullstack.py`

```python
from typing import Dict, List, Any
from .base_workflow import BaseWorkflow
from ..base.models import WorkflowStep, WorkflowState
from ..agents.analyst import AnalystAgent
from ..agents.architect import ArchitectAgent
# Import other agents as needed

class BrownfieldFullstackWorkflow(BaseWorkflow):
    def __init__(self):
        super().__init__("brownfield-fullstack")
        
        # Initialize agents
        self.analyst = AnalystAgent()
        self.architect = ArchitectAgent()
        # Initialize other agents...
    
    def _define_steps(self) -> List[WorkflowStep]:
        return [
            WorkflowStep(
                step_id="scope_classification",
                step_name="Scope Classification",
                required_agents=["analyst"],
                input_requirements={"enhancement_description": "string"},
                output_specifications={"scope_type": "string", "complexity": "string"}
            ),
            WorkflowStep(
                step_id="documentation_check",
                step_name="Documentation Assessment",
                required_agents=["analyst"],
                input_requirements={"project_context": "dict"},
                output_specifications={"documentation_status": "dict", "gaps": "list"}
            ),
            WorkflowStep(
                step_id="project_analysis",
                step_name="Project Analysis",
                required_agents=["analyst", "architect"],
                input_requirements={"codebase_info": "dict"},
                output_specifications={"analysis_report": "dict", "recommendations": "list"}
            ),
            WorkflowStep(
                step_id="prd_creation",
                step_name="PRD Creation",
                required_agents=["analyst", "po"],
                input_requirements={"requirements": "dict"},
                output_specifications={"prd_document": "dict"}
            ),
            WorkflowStep(
                step_id="architecture_decisions",
                step_name="Architecture Decisions",
                required_agents=["architect"],
                input_requirements={"prd": "dict", "current_architecture": "dict"},
                output_specifications={"architecture_plan": "dict", "decisions": "list"}
            ),
            WorkflowStep(
                step_id="story_creation",
                step_name="Story Creation",
                required_agents=["analyst", "po"],
                input_requirements={"prd": "dict", "architecture": "dict"},
                output_specifications={"user_stories": "list", "backlog": "dict"}
            )
        ]
    
    async def _execute_step_logic(self, step: WorkflowStep, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute step-specific logic."""
        
        if step.step_id == "scope_classification":
            return await self._execute_scope_classification(step_input, state)
        elif step.step_id == "documentation_check":
            return await self._execute_documentation_check(step_input, state)
        elif step.step_id == "project_analysis":
            return await self._execute_project_analysis(step_input, state)
        elif step.step_id == "prd_creation":
            return await self._execute_prd_creation(step_input, state)
        elif step.step_id == "architecture_decisions":
            return await self._execute_architecture_decisions(step_input, state)
        elif step.step_id == "story_creation":
            return await self._execute_story_creation(step_input, state)
        else:
            raise ValueError(f"Unknown step: {step.step_id}")
    
    async def _execute_scope_classification(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute scope classification step."""
        enhancement_description = step_input.get("enhancement_description", "")
        
        analysis = await self.analyst.analyze_requirements(
            f"Classify the scope and complexity of this enhancement: {enhancement_description}",
            context=state.shared_context
        )
        
        # Determine scope type based on analysis
        scope_type = "major_enhancement"  # This would be determined by analysis logic
        complexity = "high"  # This would be determined by analysis logic
        
        return {
            "scope_type": scope_type,
            "complexity": complexity,
            "analysis": analysis.model_dump(),
            "context_updates": {
                "scope_classification": {
                    "type": scope_type,
                    "complexity": complexity,
                    "analysis": analysis.model_dump()
                }
            }
        }
    
    # Implement other step execution methods...
    async def _execute_documentation_check(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        # Implementation for documentation check
        pass
    
    async def _execute_project_analysis(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        # Implementation for project analysis
        pass
    
    async def _execute_prd_creation(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        # Implementation for PRD creation
        pass
    
    async def _execute_architecture_decisions(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        # Implementation for architecture decisions
        pass
    
    async def _execute_story_creation(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        # Implementation for story creation
        pass
```

---

## Story 1.5: Integration Testing and Documentation

### Implementation Tasks

#### Task 1.5.1: Create Integration Tests
**File**: `tests/test_integration.py`

```python
import pytest
import asyncio
from bmad_agents.agents.orchestrator import BMadOrchestrator
from bmad_agents.workflows.brownfield_fullstack import BrownfieldFullstackWorkflow

class TestIntegration:
    @pytest.mark.asyncio
    async def test_orchestrator_agent_routing(self):
        orchestrator = BMadOrchestrator()
        
        # Test routing to analyst
        response = await orchestrator.route_request(
            "I need to analyze requirements for a new feature"
        )
        
        assert response.target_agent == "analyst"
        assert "analyze" in response.message.lower()
    
    @pytest.mark.asyncio
    async def test_workflow_execution(self):
        workflow = BrownfieldFullstackWorkflow()
        
        # Start workflow
        state = await workflow.start_workflow({
            "project_name": "Test Project",
            "enhancement_type": "new_feature"
        })
        
        assert state.workflow_type == "brownfield-fullstack"
        assert state.current_step == "scope_classification"
        
        # Execute first step
        result = await workflow.execute_step(state.workflow_id, {
            "enhancement_description": "Add user authentication system"
        })
        
        assert "scope_type" in result
        assert "complexity" in result
    
    @pytest.mark.asyncio
    async def test_multi_agent_coordination(self):
        orchestrator = BMadOrchestrator()
        
        # Test that orchestrator can coordinate multiple agents
        analyst_result = await orchestrator.execute_agent_request(
            "analyst",
            "Analyze requirements for user management system"
        )
        
        architect_result = await orchestrator.execute_agent_request(
            "architect",
            "Design architecture based on analyst findings",
            context={"analyst_result": analyst_result}
        )
        
        assert analyst_result is not None
        assert architect_result is not None
```

#### Task 1.5.2: Update Documentation
**File**: `docs/bmad_agents_guide.md`

```markdown
# BMad Pydantic AI Agents - User Guide

## Overview

The BMad Pydantic AI Agents system implements the complete BMad Method using the Pydantic AI framework. It provides specialized AI agents for each role in the software development process, along with workflow orchestration capabilities.

## Quick Start

### Using Individual Agents

```python
from bmad_agents.agents.analyst import AnalystAgent
from bmad_agents.agents.architect import ArchitectAgent

# Create and use an analyst agent
analyst = AnalystAgent()
analysis = await analyst.analyze_requirements(
    "Create a web application for project management"
)

# Create and use an architect agent
architect = ArchitectAgent()
architecture = await architect.design_architecture(
    "Design scalable microservices architecture"
)
```

### Using the Orchestrator

```python
from bmad_agents.agents.orchestrator import BMadOrchestrator

# Create orchestrator
orchestrator = BMadOrchestrator()

# Route requests automatically
response = await orchestrator.route_request(
    "I need to analyze requirements for a new feature"
)

# Get help
help_info = await orchestrator.get_help("workflows")
```

### Using Workflows

```python
from bmad_agents.workflows.brownfield_fullstack import BrownfieldFullstackWorkflow

# Start a workflow
workflow = BrownfieldFullstackWorkflow()
state = await workflow.start_workflow({
    "project_name": "My Project",
    "enhancement_description": "Add user authentication"
})

# Execute workflow steps
result = await workflow.execute_step(state.workflow_id, {
    "enhancement_description": "Add OAuth2 authentication system"
})
```

## Available Agents

### Analyst Agent
- **Purpose**: Requirements analysis and user story creation
- **Key Methods**: `analyze_requirements()`, `create_user_stories()`
- **Output Models**: `RequirementAnalysis`, `UserStory`

### Architect Agent
- **Purpose**: System design and technical decisions
- **Key Methods**: `design_architecture()`, `review_architecture()`
- **Output Models**: `SystemArchitecture`, `TechnicalComponent`

[Continue with other agents...]

## Workflows

### Brownfield Full-Stack Workflow
Enhances existing full-stack applications through a structured process:
1. Scope Classification
2. Documentation Assessment
3. Project Analysis
4. PRD Creation
5. Architecture Decisions
6. Story Creation

### Greenfield Full-Stack Workflow
Creates new full-stack applications from scratch:
1. Requirements Gathering
2. Architecture Design
3. Technology Selection
4. Implementation Planning
5. Development Coordination
6. Quality Assurance

## Configuration

Set up your environment variables:

```bash
# Required API keys
GOOGLE_AI_API_KEY=your_google_ai_key
OPENAI_API_KEY=your_openai_key

# Optional BMad-specific settings
BMAD_DEFAULT_MODEL=gemini-2.0-flash-exp
BMAD_AGENT_TIMEOUT=30
BMAD_MAX_CONCURRENT_AGENTS=5
BMAD_WORKFLOW_PERSISTENCE=true
```

## Best Practices

1. **Use the Orchestrator**: Let the orchestrator route requests to appropriate agents
2. **Maintain Context**: Pass relevant context between agent calls
3. **Handle Errors**: Implement proper error handling for agent interactions
4. **Monitor Performance**: Use built-in logging to monitor agent performance
5. **Persist State**: Use workflow persistence for long-running processes

## Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure all required API keys are set in your .env file
2. **Timeout Issues**: Increase `BMAD_AGENT_TIMEOUT` for complex requests
3. **Memory Issues**: Reduce `BMAD_MAX_CONCURRENT_AGENTS` if experiencing memory problems
4. **Workflow State**: Check workflow state files in `./workflow_states/` directory

### Debugging

Enable debug logging:

```python
import logging
logging.getLogger('bmad').setLevel(logging.DEBUG)
```
```

---

## Story 1.6: Production Readiness and Optimization

### Implementation Tasks

#### Task 1.6.1: Add Production Error Handling
**File**: `bmad_agents/base/error_handling.py`

```python
import asyncio
import logging
from typing import Any, Callable, Optional
from functools import wraps
from .config import config

class BMadError(Exception):
    """Base exception for BMad agents."""
    pass

class AgentTimeoutError(BMadError):
    """Raised when agent execution times out."""
    pass

class AgentExecutionError(BMadError):
    """Raised when agent execution fails."""
    pass

class WorkflowError(BMadError):
    """Raised when workflow execution fails."""
    pass

def with_retry(max_retries: int = None, backoff_factor: float = 1.0):
    """Decorator to add retry logic to agent methods."""
    max_retries = max_retries or config.max_retries
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = backoff_factor * (2 ** attempt)
                        logging.getLogger('bmad').warning(
                            f"Attempt {attempt + 1} failed, retrying in {wait_time}s: {str(e)}"
                        )
                        await asyncio.sleep(wait_time)
                    else:
                        logging.getLogger('bmad').error(
                            f"All {max_retries + 1} attempts failed: {str(e)}"
                        )
            
            raise AgentExecutionError(f"Failed after {max_retries + 1} attempts") from last_exception
        
        return wrapper
    return decorator

def with_timeout(timeout: int = None):
    """Decorator to add timeout to agent methods."""
    timeout = timeout or config.agent_timeout
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout)
            except asyncio.TimeoutError:
                raise AgentTimeoutError(f"Agent execution timed out after {timeout}s")
        
        return wrapper
    return decorator

class CircuitBreaker:
    """Circuit breaker pattern for API calls."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half-open
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        if self.state == "open":
            if self._should_attempt_reset():
                self.state = "half-open"
            else:
                raise AgentExecutionError("Circuit breaker is open")
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise
    
    def _should_attempt_reset(self) -> bool:
        return (
            self.last_failure_time and
            (asyncio.get_event_loop().time() - self.last_failure_time) > self.recovery_timeout
        )
    
    def _on_success(self):
        self.failure_count = 0
        self.state = "closed"
    
    def _on_failure(self):
        self.failure_count += 1
        self.last_failure_time = asyncio.get_event_loop().time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"
```

#### Task 1.6.2: Add Performance Monitoring
**File**: `bmad_agents/base/monitoring.py`

```python
import time
import psutil
import asyncio
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import json

@dataclass
class PerformanceMetrics:
    agent_role: str
    operation: str
    duration: float
    memory_usage: float
    cpu_usage: float
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None

class PerformanceMonitor:
    def __init__(self):
        self.metrics: List[PerformanceMetrics] = []
        self.start_times: Dict[str, float] = {}
    
    def start_operation(self, operation_id: str):
        """Start timing an operation."""
        self.start_times[operation_id] = time.time()
    
    def end_operation(self, operation_id: str, agent_role: str, operation: str, success: bool = True, error: str = None):
        """End timing an operation and record metrics."""
        if operation_id not in self.start_times:
            return
        
        duration = time.time() - self.start_times[operation_id]
        del self.start_times[operation_id]
        
        # Get system metrics
        process = psutil.Process()
        memory_usage = process.memory_info().rss / 1024 / 1024  # MB
        cpu_usage = process.cpu_percent()
        
        metrics = PerformanceMetrics(
            agent_role=agent_role,
            operation=operation,
            duration=duration,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage,
            timestamp=datetime.now(),
            success=success,
            error_message=error
        )
        
        self.metrics.append(metrics)
        
        # Log metrics
        import logging
        logger = logging.getLogger('bmad.performance')
        logger.info(f"Operation {operation} completed in {duration:.2f}s (Memory: {memory_usage:.1f}MB, CPU: {cpu_usage:.1f}%)")
    
    def get_metrics_summary(self, agent_role: str = None) -> Dict[str, Any]:
        """Get summary of performance metrics."""
        filtered_metrics = self.metrics
        if agent_role:
            filtered_metrics = [m for m in self.metrics if m.agent_role == agent_role]
        
        if not filtered_metrics:
            return {}
        
        total_operations = len(filtered_metrics)
        successful_operations = len([m for m in filtered_metrics if m.success])
        avg_duration = sum(m.duration for m in filtered_metrics) / total_operations
        avg_memory = sum(m.memory_usage for m in filtered_metrics) / total_operations
        
        return {
            "total_operations": total_operations,
            "successful_operations": successful_operations,
            "success_rate": successful_operations / total_operations,
            "average_duration": avg_duration,
            "average_memory_usage": avg_memory,
            "last_updated": datetime.now().isoformat()
        }
    
    def export_metrics(self, filename: str):
        """Export metrics to JSON file."""
        metrics_data = [
            {
                "agent_role": m.agent_role,
                "operation": m.operation,
                "duration": m.duration,
                "memory_usage": m.memory_usage,
                "cpu_usage": m.cpu_usage,
                "timestamp": m.timestamp.isoformat(),
                "success": m.success,
                "error_message": m.error_message
            }
            for m in self.metrics
        ]
        
        with open(filename, 'w') as f:
            json.dump(metrics_data, f, indent=2)

# Global performance monitor instance
performance_monitor = PerformanceMonitor()
```

### Final Integration

#### Task 1.6.3: Create Main Module
**File**: `bmad_agents/__init__.py`

```python
"""
BMad Pydantic AI Agents

A comprehensive implementation of the BMad Method using Pydantic AI framework.
Provides specialized agents for software development roles and workflow orchestration.
"""

__version__ = "1.0.0"

# Import main classes for easy access
from .agents.orchestrator import BMadOrchestrator
from .agents.analyst import AnalystAgent
from .agents.architect import ArchitectAgent
from .workflows.brownfield_fullstack import BrownfieldFullstackWorkflow
from .base.bmad_agent import BMadAgent
from .base.config import config
from .base.logging_config import setup_logging

# Initialize logging
setup_logging()

__all__ = [
    "BMadOrchestrator",
    "AnalystAgent",
    "ArchitectAgent",
    "BrownfieldFullstackWorkflow",
    "BMadAgent",
    "config",
    "setup_logging"
]
```

#### Task 1.6.4: Create Example Usage
**File**: `bmad_agents/examples/complete_example.py`

```python
"""
Complete example demonstrating BMad Pydantic AI Agents usage.
"""

import asyncio
from bmad_agents import BMadOrchestrator, BrownfieldFullstackWorkflow

async def main():
    print("BMad Pydantic AI Agents - Complete Example")
    print("=" * 50)
    
    # Initialize orchestrator
    orchestrator = BMadOrchestrator()
    
    # Example 1: Using orchestrator for request routing
    print("\n1. Request Routing Example:")
    response = await orchestrator.route_request(
        "I need to analyze requirements for adding user authentication to my web app"
    )
    print(f"Action: {response.action}")
    print(f"Target Agent: {response.target_agent}")
    print(f"Message: {response.message}")
    
    # Example 2: Direct agent usage
    print("\n2. Direct Agent Usage:")
    analysis = await orchestrator.execute_agent_request(
        "analyst",
        "Analyze requirements for a user authentication system with OAuth2 support"
    )
    print(f"Analysis completed: {type(analysis).__name__}")
    
    # Example 3: Workflow execution
    print("\n3. Workflow Execution:")
    workflow = BrownfieldFullstackWorkflow()
    
    # Start workflow
    state = await workflow.start_workflow({
        "project_name": "E-commerce Platform",
        "enhancement_type": "authentication_system"
    })
    print(f"Workflow started: {state.workflow_id}")
    print(f"Current step: {state.current_step}")
    
    # Execute first step
    result = await workflow.execute_step(state.workflow_id, {
        "enhancement_description": "Add OAuth2 authentication with Google and GitHub providers"
    })
    print(f"Step completed: {result.get('scope_type', 'Unknown')}")
    
    # Example 4: Help system
    print("\n4. Help System:")
    help_info = await orchestrator.get_help("agents")
    print(f"Help provided: {len(help_info)} characters")
    
    print("\nExample completed successfully!")

if __name__ == "__main__":
    asyncio.run(main())
```

This comprehensive story breakdown provides detailed implementation guidance for each component of the BMad Pydantic AI Agents system, ensuring a structured and maintainable implementation that extends the existing Pydantic AI framework while maintaining full backward compatibility.