import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from pathlib import Path
import logging
from .communication import WorkflowState, WorkflowStep, AgentMessage

class StateManager:
    """
    Manages workflow state persistence and retrieval.
    Handles saving/loading workflow states, message history, and agent context.
    """
    
    def __init__(self, state_dir: str = ".bmad_state"):
        self.state_dir = Path(state_dir)
        self.state_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        (self.state_dir / "workflows").mkdir(exist_ok=True)
        (self.state_dir / "messages").mkdir(exist_ok=True)
        (self.state_dir / "agents").mkdir(exist_ok=True)
        
        self.logger = logging.getLogger("bmad.state_manager")
        self.logger.info(f"StateManager initialized with state directory: {self.state_dir}")
    
    def save_workflow_state(self, workflow_state: WorkflowState) -> bool:
        """
        Save workflow state to persistent storage.
        """
        try:
            workflow_file = self.state_dir / "workflows" / f"{workflow_state.workflow_id}.json"
            
            # Update the updated_at timestamp
            workflow_state.updated_at = datetime.now()
            
            with open(workflow_file, 'w', encoding='utf-8') as f:
                json.dump(workflow_state.model_dump(), f, indent=2, default=str)
            
            self.logger.info(f"Workflow state saved: {workflow_state.workflow_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save workflow state {workflow_state.workflow_id}: {str(e)}")
            return False
    
    def load_workflow_state(self, workflow_id: str) -> Optional[WorkflowState]:
        """
        Load workflow state from persistent storage.
        """
        try:
            workflow_file = self.state_dir / "workflows" / f"{workflow_id}.json"
            
            if not workflow_file.exists():
                self.logger.warning(f"Workflow state file not found: {workflow_id}")
                return None
            
            with open(workflow_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Convert datetime strings back to datetime objects
            if 'started_at' in data:
                data['started_at'] = datetime.fromisoformat(data['started_at'])
            if 'updated_at' in data:
                data['updated_at'] = datetime.fromisoformat(data['updated_at'])
            
            workflow_state = WorkflowState(**data)
            self.logger.info(f"Workflow state loaded: {workflow_id}")
            return workflow_state
            
        except Exception as e:
            self.logger.error(f"Failed to load workflow state {workflow_id}: {str(e)}")
            return None
    
    def list_workflows(self) -> List[str]:
        """
        List all available workflow IDs.
        """
        try:
            workflow_files = list((self.state_dir / "workflows").glob("*.json"))
            workflow_ids = [f.stem for f in workflow_files]
            return workflow_ids
        except Exception as e:
            self.logger.error(f"Failed to list workflows: {str(e)}")
            return []
    
    def delete_workflow_state(self, workflow_id: str) -> bool:
        """
        Delete workflow state from persistent storage.
        """
        try:
            workflow_file = self.state_dir / "workflows" / f"{workflow_id}.json"
            
            if workflow_file.exists():
                workflow_file.unlink()
                self.logger.info(f"Workflow state deleted: {workflow_id}")
                return True
            else:
                self.logger.warning(f"Workflow state file not found for deletion: {workflow_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to delete workflow state {workflow_id}: {str(e)}")
            return False
    
    def save_message(self, message: AgentMessage) -> bool:
        """
        Save agent message to message history.
        """
        try:
            # Create date-based directory structure
            date_dir = self.state_dir / "messages" / message.timestamp.strftime("%Y-%m-%d")
            date_dir.mkdir(exist_ok=True)
            
            message_file = date_dir / f"{message.id}.json"
            
            with open(message_file, 'w', encoding='utf-8') as f:
                json.dump(message.model_dump(), f, indent=2, default=str)
            
            self.logger.debug(f"Message saved: {message.id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save message {message.id}: {str(e)}")
            return False
    
    def get_messages_by_workflow(self, workflow_id: str) -> List[AgentMessage]:
        """
        Retrieve all messages associated with a workflow.
        """
        try:
            messages = []
            messages_dir = self.state_dir / "messages"
            
            # Search through all date directories
            for date_dir in messages_dir.iterdir():
                if date_dir.is_dir():
                    for message_file in date_dir.glob("*.json"):
                        try:
                            with open(message_file, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            
                            if data.get('workflow_id') == workflow_id:
                                # Convert timestamp string back to datetime
                                if 'timestamp' in data:
                                    data['timestamp'] = datetime.fromisoformat(data['timestamp'])
                                
                                message = AgentMessage(**data)
                                messages.append(message)
                        except Exception as e:
                            self.logger.warning(f"Failed to load message from {message_file}: {str(e)}")
            
            # Sort messages by timestamp
            messages.sort(key=lambda m: m.timestamp)
            return messages
            
        except Exception as e:
            self.logger.error(f"Failed to get messages for workflow {workflow_id}: {str(e)}")
            return []
    
    def save_agent_context(self, agent_role: str, context: Dict[str, Any]) -> bool:
        """
        Save agent-specific context data.
        """
        try:
            agent_file = self.state_dir / "agents" / f"{agent_role}.json"
            
            # Add timestamp to context
            context['updated_at'] = datetime.now().isoformat()
            
            with open(agent_file, 'w', encoding='utf-8') as f:
                json.dump(context, f, indent=2, default=str)
            
            self.logger.debug(f"Agent context saved: {agent_role}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save agent context for {agent_role}: {str(e)}")
            return False
    
    def load_agent_context(self, agent_role: str) -> Optional[Dict[str, Any]]:
        """
        Load agent-specific context data.
        """
        try:
            agent_file = self.state_dir / "agents" / f"{agent_role}.json"
            
            if not agent_file.exists():
                return None
            
            with open(agent_file, 'r', encoding='utf-8') as f:
                context = json.load(f)
            
            self.logger.debug(f"Agent context loaded: {agent_role}")
            return context
            
        except Exception as e:
            self.logger.error(f"Failed to load agent context for {agent_role}: {str(e)}")
            return None
    
    def cleanup_old_data(self, days_to_keep: int = 30) -> bool:
        """
        Clean up old message data to prevent storage bloat.
        """
        try:
            cutoff_date = datetime.now().date() - timedelta(days=days_to_keep)
            messages_dir = self.state_dir / "messages"
            
            deleted_count = 0
            for date_dir in messages_dir.iterdir():
                if date_dir.is_dir():
                    try:
                        dir_date = datetime.strptime(date_dir.name, "%Y-%m-%d").date()
                        if dir_date < cutoff_date:
                            # Remove entire directory
                            for file in date_dir.iterdir():
                                file.unlink()
                            date_dir.rmdir()
                            deleted_count += 1
                    except ValueError:
                        # Skip directories that don't match date format
                        continue
            
            self.logger.info(f"Cleaned up {deleted_count} old message directories")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old data: {str(e)}")
            return False