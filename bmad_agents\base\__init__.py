"""BMad Agents Base Infrastructure

This module provides the foundational components for the BMad Method agents system,
including base agent classes, communication models, state management, configuration,
and logging infrastructure.
"""

from .bmad_agent import BMadAgent
from .communication import Priority
from .models import (
    AgentMessage,
    AgentRequest, 
    AgentResponse,
    WorkflowState,
    WorkflowStep,
    MessageType,
    ResponseStatus
)
from .state_manager import StateManager
from .config import BMadConfig, AgentConfig, WorkflowConfig
from .logging_config import setup_logging, get_logger, BMadLogger

__all__ = [
    # Core agent infrastructure
    'BMadAgent',
    
    # Communication models
    'AgentMessage',
    'AgentRequest',
    'AgentResponse', 
    'WorkflowState',
    'WorkflowStep',
    'MessageType',
    'ResponseStatus',
    'Priority',
    
    # State management
    'StateManager',
    
    # Configuration
    'BMadConfig',
    'AgentConfig',
    'WorkflowConfig',
    
    # Logging
    'setup_logging',
    'get_logger',
    'BMadLogger'
]

# Version information
__version__ = '1.0.0'
__author__ = 'BMad Method Team'
__description__ = 'Base infrastructure for BMad Method AI agents'