{"tests/test_workflows.py::TestWorkflowFactory": true, "tests/test_workflows.py::TestBaseWorkflow": true, "tests/test_workflows.py::TestBrownfieldWorkflow": true, "tests/test_workflows.py::TestGreenfieldWorkflow": true, "tests/test_workflows.py::TestBaseWorkflow::test_start_workflow": true, "tests/test_workflows.py::TestBaseWorkflow::test_execute_step": true, "tests/test_workflows.py::TestBaseWorkflow::test_get_next_step": true, "tests/test_workflows.py::TestBrownfieldWorkflow::test_execute_scope_classification": true, "tests/test_workflows.py::TestGreenfieldWorkflow::test_execute_requirements_gathering": true}