#!/usr/bin/env python3
"""
Example using Google Gemini models with Pydantic AI.
"""

import os
import asyncio
from dotenv import load_dotenv
from pydantic_ai import Agent
from pydantic import BaseModel

# Load environment variables
load_dotenv()

# Define response models
class CodeAnalysis(BaseModel):
    language: str
    complexity: str
    suggestions: list[str]
    rating: int  # 1-10 scale

class CreativeStory(BaseModel):
    title: str
    genre: str
    main_character: str
    plot_summary: str
    word_count_estimate: int

# Create different agents for different tasks
code_agent = Agent(
    'gemini-2.5-flash',  # Latest Gemini 2.5 Flash model
    output_type=CodeAnalysis,
    system_prompt=(
        'You are an expert code reviewer. Analyze code snippets and provide '
        'constructive feedback, suggestions for improvement, and rate the code quality.'
    ),
)

creative_agent = Agent(
    'gemini-2.5-flash',  # Using the same latest model for creative tasks
    output_type=CreativeStory,
    system_prompt=(
        'You are a creative writing assistant. Generate engaging story ideas '
        'with well-developed characters and interesting plots.'
    ),
)

async def test_code_analysis():
    """Test the code analysis agent."""
    print("🔍 Testing Code Analysis with Gemini 2.5 Flash...")

    code_snippet = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)
    """
    
    try:
        result = await code_agent.run(
            f"Analyze this Python code:\n{code_snippet}"
        )
        
        print(f"Language: {result.output.language}")
        print(f"Complexity: {result.output.complexity}")
        print(f"Rating: {result.output.rating}/10")
        print("Suggestions:")
        for suggestion in result.output.suggestions:
            print(f"  • {suggestion}")
        print()
        
    except Exception as e:
        print(f"❌ Error in code analysis: {e}")

async def test_creative_writing():
    """Test the creative writing agent."""
    print("✨ Testing Creative Writing with Gemini 2.5 Flash...")

    try:
        result = await creative_agent.run(
            "Create a short story idea about a programmer who discovers their code can alter reality"
        )
        
        print(f"Title: {result.output.title}")
        print(f"Genre: {result.output.genre}")
        print(f"Main Character: {result.output.main_character}")
        print(f"Plot: {result.output.plot_summary}")
        print(f"Estimated Length: {result.output.word_count_estimate} words")
        print()
        
    except Exception as e:
        print(f"❌ Error in creative writing: {e}")

async def main():
    """Main function to run all examples."""
    # Check if API key is set
    if not os.getenv('GOOGLE_AI_API_KEY') or os.getenv('GOOGLE_AI_API_KEY') == 'your_google_ai_api_key_here':
        print("❌ Please set your GOOGLE_AI_API_KEY in the .env file")
        print("1. Go to https://aistudio.google.com/app/apikey")
        print("2. Create or select a project")
        print("3. Click 'Create API Key'")
        print("4. Copy the key and paste it in your .env file")
        return
    
    print("🚀 Testing Google Gemini 2.5 Flash with Pydantic AI\n")
    
    # Run both examples
    await test_code_analysis()
    await test_creative_writing()
    
    print("✅ All tests completed!")

if __name__ == '__main__':
    asyncio.run(main())
