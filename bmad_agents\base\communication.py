from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum

class MessageType(str, Enum):
    """Types of messages that can be sent between agents."""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    ERROR = "error"
    WORKFLOW_UPDATE = "workflow_update"

class Priority(str, Enum):
    """Message priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

class AgentMessage(BaseModel):
    """Base message structure for agent communication."""
    id: str = Field(description="Unique message identifier")
    type: MessageType = Field(description="Type of message")
    from_agent: str = Field(description="Sending agent role")
    to_agent: Optional[str] = Field(default=None, description="Target agent role (None for broadcast)")
    timestamp: datetime = Field(default_factory=datetime.now, description="Message timestamp")
    priority: Priority = Field(default=Priority.NORMAL, description="Message priority")
    content: Dict[str, Any] = Field(description="Message content")
    correlation_id: Optional[str] = Field(default=None, description="ID to correlate related messages")
    workflow_id: Optional[str] = Field(default=None, description="Associated workflow ID")

class AgentRequest(BaseModel):
    """Request structure for agent tasks."""
    id: str = Field(description="Unique request identifier")
    task_type: str = Field(description="Type of task being requested")
    parameters: Dict[str, Any] = Field(description="Task parameters")
    context: Optional[Dict[str, Any]] = Field(default=None, description="Additional context")
    deadline: Optional[datetime] = Field(default=None, description="Task deadline")
    requester: str = Field(description="Agent or system making the request")
    workflow_step: Optional[str] = Field(default=None, description="Current workflow step")

class AgentResponse(BaseModel):
    """Response structure from agent tasks."""
    request_id: str = Field(description="ID of the original request")
    agent: str = Field(description="Responding agent role")
    status: str = Field(description="Response status (success, error, partial)")
    result: Optional[Dict[str, Any]] = Field(default=None, description="Task result")
    error: Optional[str] = Field(default=None, description="Error message if status is error")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional response metadata")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
    next_steps: Optional[List[str]] = Field(default=None, description="Suggested next steps")

class WorkflowState(BaseModel):
    """Current state of a workflow."""
    workflow_id: str = Field(description="Unique workflow identifier")
    workflow_type: str = Field(description="Type of workflow (e.g., brownfield-fullstack)")
    current_step: str = Field(description="Current workflow step")
    status: str = Field(description="Workflow status (active, paused, completed, error)")
    progress: float = Field(ge=0, le=1, description="Workflow progress (0.0 to 1.0)")
    started_at: datetime = Field(description="Workflow start time")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update time")
    context: Dict[str, Any] = Field(description="Workflow context and data")
    participants: List[str] = Field(description="List of participating agent roles")
    completed_steps: List[str] = Field(description="List of completed steps")
    pending_steps: List[str] = Field(description="List of pending steps")

class WorkflowStep(BaseModel):
    """Definition of a workflow step."""
    step_id: str = Field(description="Unique step identifier")
    name: str = Field(description="Human-readable step name")
    description: str = Field(description="Step description")
    responsible_agent: str = Field(description="Agent responsible for this step")
    dependencies: List[str] = Field(description="List of step IDs that must complete first")
    estimated_duration: Optional[int] = Field(default=None, description="Estimated duration in minutes")
    inputs: Dict[str, Any] = Field(description="Required inputs for this step")
    outputs: Dict[str, Any] = Field(description="Expected outputs from this step")
    validation_criteria: List[str] = Field(description="Criteria to validate step completion")