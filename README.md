# Pydantic AI Project

This project is set up with Pydantic AI and all necessary prerequisites.

## Setup Complete ✅

- Python 3.12.2 detected
- Virtual environment created (`venv/`)
- Pydantic AI v0.4.11 installed with all dependencies
- Requirements file generated

## Project Structure

```
BMADPydanticAgents/
├── venv/                 # Virtual environment
├── test_pydantic_ai.py   # Installation test script
├── example_agent.py      # Example weather agent
├── gemini_example.py     # Google Gemini 2.5 Flash examples
├── gemini_2_advanced.py  # Advanced Gemini 2.5 Flash demos
├── test_gemini.py        # Gemini API connection test
├── .env                  # Your API keys (created)
├── .env.example          # API key template
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## Getting Started

### 1. Activate Virtual Environment

```bash
# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 2. Set Up Google Gemini API Key

1. **Get your API key**:
   - Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
   - Sign in with your Google account
   - Create or select a project
   - Click "Create API Key"
   - Copy the generated API key

2. **Configure your .env file**:
   - Open the `.env` file (already created for you)
   - Replace `your_google_ai_api_key_here` with your actual API key
   - Save the file

### 3. Test Gemini Connection

```bash
python test_gemini.py
```

### 4. Run Gemini Examples

```bash
# Basic Gemini 2.5 Flash examples
python gemini_example.py

# Advanced Gemini 2.5 Flash demonstrations
python gemini_2_advanced.py
```

### 5. Test General Installation

```bash
python test_pydantic_ai.py
```

## Supported AI Providers

Pydantic AI supports multiple AI providers:

- **OpenAI**: GPT-4, GPT-3.5, etc.
- **Anthropic**: Claude 3.5 Sonnet, Claude 3 Haiku, etc.
- **Google**: Gemini 2.5 Flash (latest), Gemini Pro, Gemini Flash
- **Groq**: Fast inference for Llama, Mixtral models
- **Mistral AI**: Mistral models
- **Cohere**: Command models
- **AWS Bedrock**: Various models through AWS

## Next Steps

1. Set up your preferred AI provider API keys
2. Explore the example agent
3. Create your own agents for specific tasks
4. Build AI-powered applications!

## Resources

- [Pydantic AI Documentation](https://ai.pydantic.dev/)
- [Pydantic AI GitHub](https://github.com/pydantic/pydantic-ai)
- [API Provider Setup Guides](https://ai.pydantic.dev/install/)
