import pytest
import async<PERSON>
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch
from bmad_agents.workflows import (
    BaseWorkflow, 
    BrownfieldFullstackWorkflow, 
    GreenfieldFullstackWorkflow,
    WorkflowFactory
)
from bmad_agents.base.models import WorkflowState, WorkflowStep
from bmad_agents.base.state_manager import StateManager

class TestWorkflowFactory:
    """Test cases for WorkflowFactory."""
    
    def test_create_brownfield_workflow(self):
        """Test creating a brownfield workflow."""
        workflow = WorkflowFactory.create_workflow("brownfield-fullstack")
        assert isinstance(workflow, BrownfieldFullstackWorkflow)
        assert workflow.workflow_type == "brownfield-fullstack"
    
    def test_create_greenfield_workflow(self):
        """Test creating a greenfield workflow."""
        workflow = WorkflowFactory.create_workflow("greenfield-fullstack")
        assert isinstance(workflow, GreenfieldFullstackWorkflow)
        assert workflow.workflow_type == "greenfield-fullstack"
    
    def test_create_unknown_workflow(self):
        """Test creating an unknown workflow type raises ValueError."""
        with pytest.raises(ValueError, match="Unknown workflow type: unknown"):
            WorkflowFactory.create_workflow("unknown")
    
    def test_get_available_workflows(self):
        """Test getting available workflow types."""
        workflows = WorkflowFactory.get_available_workflows()
        assert "brownfield-fullstack" in workflows
        assert "greenfield-fullstack" in workflows
        assert len(workflows) >= 2
    
    def test_is_workflow_available(self):
        """Test checking workflow availability."""
        assert WorkflowFactory.is_workflow_available("brownfield-fullstack")
        assert WorkflowFactory.is_workflow_available("greenfield-fullstack")
        assert not WorkflowFactory.is_workflow_available("unknown")
    
    def test_get_workflow_info(self):
        """Test getting workflow information."""
        info = WorkflowFactory.get_workflow_info("brownfield-fullstack")
        assert info is not None
        assert info["type"] == "brownfield-fullstack"
        assert info["class_name"] == "BrownfieldFullstackWorkflow"
        assert "description" in info
        
        # Test unknown workflow
        info = WorkflowFactory.get_workflow_info("unknown")
        assert info is None

class TestBaseWorkflow:
    """Test cases for BaseWorkflow."""
    
    class MockWorkflow(BaseWorkflow):
        """Mock workflow for testing."""
        
        def __init__(self):
            super().__init__("mock-workflow")
        
        def _define_steps(self):
            return [
                WorkflowStep(
                    step_id="step1",
                    step_name="Step 1",
                    required_agents=["agent1"],
                    input_requirements={"input1": "string"},
                    output_specifications={"output1": "string"}
                ),
                WorkflowStep(
                    step_id="step2",
                    step_name="Step 2",
                    required_agents=["agent2"],
                    input_requirements={"input2": "string"},
                    output_specifications={"output2": "string"}
                )
            ]
        
        async def _execute_step_logic(self, step, step_input, state):
            return {"result": f"executed_{step.step_id}"}
    
    @pytest.fixture
    def mock_workflow(self):
        return self.MockWorkflow()
    
    @pytest.fixture
    def mock_state_manager(self):
        with patch('bmad_agents.workflows.base_workflow.StateManager') as mock:
            mock_instance = AsyncMock()
            mock.return_value = mock_instance
            yield mock_instance
    
    @pytest.mark.asyncio
    async def test_start_workflow(self, mock_workflow, mock_state_manager):
        """Test starting a workflow."""
        mock_state_manager.save_state = AsyncMock()
        
        workflow_id = await mock_workflow.start_workflow(
            workflow_input={"initial_data": "test"}
        )
        
        assert workflow_id is not None
        assert len(workflow_id) > 0
        mock_state_manager.save_state.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_step(self, mock_workflow, mock_state_manager):
        """Test executing a workflow step."""
        # Setup mock state
        mock_state = WorkflowState(
            workflow_id="test-id",
            workflow_type="mock-workflow",
            current_step_index=0,
            steps=mock_workflow._define_steps(),
            shared_context={},
            step_results={}
        )
        
        mock_state_manager.load_state = AsyncMock(return_value=mock_state)
        mock_state_manager.save_state = AsyncMock()
        
        result = await mock_workflow.execute_step(
            workflow_id="test-id",
            step_input={"input1": "test_input"}
        )
        
        assert result["result"] == "executed_step1"
        mock_state_manager.load_state.assert_called_once_with("test-id")
        mock_state_manager.save_state.assert_called()
    
    def test_get_step(self, mock_workflow):
        """Test getting a step by ID."""
        step = mock_workflow._get_step("step1")
        assert step is not None
        assert step.step_id == "step1"
        assert step.step_name == "Step 1"
        
        # Test non-existent step
        step = mock_workflow._get_step("nonexistent")
        assert step is None
    
    def test_get_next_step(self, mock_workflow):
        """Test getting the next step."""
        steps = mock_workflow._define_steps()
        
        # Test getting next step
        next_step = mock_workflow._get_next_step(steps, 0)
        assert next_step is not None
        assert next_step.step_id == "step2"
        
        # Test last step
        next_step = mock_workflow._get_next_step(steps, 1)
        assert next_step is None
        
        # Test invalid index
        next_step = mock_workflow._get_next_step(steps, 10)
        assert next_step is None

class TestBrownfieldWorkflow:
    """Test cases for BrownfieldFullstackWorkflow."""
    
    @pytest.fixture
    def brownfield_workflow(self):
        return BrownfieldFullstackWorkflow()
    
    def test_workflow_initialization(self, brownfield_workflow):
        """Test workflow initialization."""
        assert brownfield_workflow.workflow_type == "brownfield-fullstack"
        assert hasattr(brownfield_workflow, 'analyst')
        assert hasattr(brownfield_workflow, 'architect')
        assert hasattr(brownfield_workflow, 'po')
    
    def test_define_steps(self, brownfield_workflow):
        """Test step definition."""
        steps = brownfield_workflow._define_steps()
        assert len(steps) > 0
        
        # Check first step
        first_step = steps[0]
        assert first_step.step_id == "scope_classification"
        assert first_step.step_name == "Scope Classification"
        assert "analyst" in first_step.required_agents
    
    @pytest.mark.asyncio
    async def test_execute_scope_classification(self, brownfield_workflow):
        """Test scope classification step execution."""
        with patch.object(brownfield_workflow.analyst, 'analyze_project_scope') as mock_analyze:
            mock_analyze.return_value = AsyncMock()
            mock_analyze.return_value.model_dump.return_value = {
                "scope_type": "medium",
                "complexity": "moderate"
            }
            
            mock_state = WorkflowState(
                workflow_id="test",
                workflow_type="brownfield-fullstack",
                current_step_index=0,
                steps=[],
                shared_context={},
                step_results={}
            )
            
            step_input = {"project_description": "Test project"}
            result = await brownfield_workflow._execute_scope_classification(step_input, mock_state)
            
            assert "scope_analysis" in result
            assert "context_updates" in result
            mock_analyze.assert_called_once()

class TestGreenfieldWorkflow:
    """Test cases for GreenfieldFullstackWorkflow."""
    
    @pytest.fixture
    def greenfield_workflow(self):
        return GreenfieldFullstackWorkflow()
    
    def test_workflow_initialization(self, greenfield_workflow):
        """Test workflow initialization."""
        assert greenfield_workflow.workflow_type == "greenfield-fullstack"
        assert hasattr(greenfield_workflow, 'analyst')
        assert hasattr(greenfield_workflow, 'architect')
        assert hasattr(greenfield_workflow, 'ux')
    
    def test_define_steps(self, greenfield_workflow):
        """Test step definition."""
        steps = greenfield_workflow._define_steps()
        assert len(steps) > 0
        
        # Check first step
        first_step = steps[0]
        assert first_step.step_id == "requirements_gathering"
        assert first_step.step_name == "Requirements Gathering"
        assert "analyst" in first_step.required_agents
        assert "po" in first_step.required_agents
    
    @pytest.mark.asyncio
    async def test_execute_requirements_gathering(self, greenfield_workflow):
        """Test requirements gathering step execution."""
        with patch.object(greenfield_workflow.analyst, 'analyze_requirements') as mock_analyze, \
             patch.object(greenfield_workflow.po, 'create_user_personas') as mock_personas:
            
            mock_analyze.return_value = AsyncMock()
            mock_analyze.return_value.model_dump.return_value = {
                "requirements": "test requirements"
            }
            
            mock_personas.return_value = [AsyncMock()]
            mock_personas.return_value[0].model_dump.return_value = {
                "persona": "test persona"
            }
            
            mock_state = WorkflowState(
                workflow_id="test",
                workflow_type="greenfield-fullstack",
                current_step_index=0,
                steps=[],
                shared_context={},
                step_results={}
            )
            
            step_input = {
                "project_vision": "Test vision",
                "stakeholder_input": {"stakeholder": "input"}
            }
            
            result = await greenfield_workflow._execute_requirements_gathering(step_input, mock_state)
            
            assert "requirements_document" in result
            assert "user_personas" in result
            assert "context_updates" in result
            mock_analyze.assert_called_once()
            mock_personas.assert_called_once()

if __name__ == "__main__":
    pytest.main([__file__])