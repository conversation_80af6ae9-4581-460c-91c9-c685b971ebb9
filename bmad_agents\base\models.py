from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum

class MessageType(str, Enum):
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    ERROR = "error"

class ResponseStatus(str, Enum):
    SUCCESS = "success"
    ERROR = "error"
    PENDING = "pending"
    TIMEOUT = "timeout"

class AgentMessage(BaseModel):
    from_agent: str
    to_agent: str
    message_type: MessageType
    content: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)
    correlation_id: str
    metadata: Dict[str, Any] = Field(default_factory=dict)

class AgentRequest(BaseModel):
    request_id: str
    agent_role: str
    action: str
    parameters: Dict[str, Any]
    context: Dict[str, Any] = Field(default_factory=dict)
    timeout: Optional[int] = 30

class AgentResponse(BaseModel):
    request_id: str
    agent_role: str
    status: ResponseStatus
    result: Dict[str, Any]
    metadata: Dict[str, Any] = Field(default_factory=dict)
    execution_time: Optional[float] = None
    error_message: Optional[str] = None

class WorkflowState(BaseModel):
    workflow_id: str
    workflow_type: str
    current_step: str
    completed_steps: List[str] = Field(default_factory=list)
    agent_contexts: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    shared_context: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    status: str = "active"

class WorkflowStep(BaseModel):
    step_id: str
    step_name: str
    required_agents: List[str]
    input_requirements: Dict[str, Any]
    output_specifications: Dict[str, Any]
    dependencies: List[str] = Field(default_factory=list)
    timeout: int = 300  # 5 minutes default