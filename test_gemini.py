#!/usr/bin/env python3
"""
Simple test to verify Google Gemini API key is working.
"""

import os
import asyncio
from dotenv import load_dotenv
from pydantic_ai import Agent

# Load environment variables
load_dotenv()

async def test_gemini_connection():
    """Test basic connection to Gemini."""
    
    # Check if API key is set
    api_key = os.getenv('GOOGLE_AI_API_KEY')
    if not api_key or api_key == 'your_google_ai_api_key_here':
        print("❌ GOOGLE_AI_API_KEY not set properly in .env file")
        print("\nTo fix this:")
        print("1. Open the .env file in your project")
        print("2. Replace 'your_google_ai_api_key_here' with your actual API key")
        print("3. Save the file and run this test again")
        return False
    
    print("🔑 API key found, testing connection...")
    
    # Create a simple agent with the latest Gemini 2.5 Flash model
    agent = Agent('gemini-2.5-flash')
    
    try:
        # Simple test query
        result = await agent.run("Say 'Hello from <PERSON>!' and tell me what model you are.")
        print(f"✅ Connection successful!")
        print(f"🤖 Gemini response: {result.output}")
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("\nPossible issues:")
        print("- Invalid API key")
        print("- Network connectivity problems")
        print("- API quota exceeded")
        return False

if __name__ == '__main__':
    print("🧪 Testing Google Gemini API connection...\n")
    success = asyncio.run(test_gemini_connection())
    
    if success:
        print("\n🎉 Setup complete! You can now use Gemini models.")
        print("Try running: python gemini_example.py")
    else:
        print("\n🔧 Please fix the issues above and try again.")
