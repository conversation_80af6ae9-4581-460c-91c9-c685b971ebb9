import time
import psutil
import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import logging
from .config import config

@dataclass
class PerformanceMetrics:
    """Data class for storing performance metrics."""
    agent_role: str
    operation: str
    duration: float
    memory_usage: float
    cpu_usage: float
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None
    request_size: Optional[int] = None
    response_size: Optional[int] = None

class PerformanceMonitor:
    """Monitor and track performance metrics for BMad agents."""
    
    def __init__(self):
        self.metrics: List[PerformanceMetrics] = []
        self.start_times: Dict[str, float] = {}
        self.logger = logging.getLogger('bmad.performance')
        self._max_metrics = 10000  # Prevent memory issues
    
    def start_operation(self, operation_id: str) -> None:
        """Start timing an operation.
        
        Args:
            operation_id: Unique identifier for the operation
        """
        self.start_times[operation_id] = time.time()
        self.logger.debug(f"Started operation: {operation_id}")
    
    def end_operation(
        self, 
        operation_id: str, 
        agent_role: str, 
        operation: str, 
        success: bool = True, 
        error: str = None,
        request_size: int = None,
        response_size: int = None
    ) -> None:
        """End timing an operation and record metrics.
        
        Args:
            operation_id: Unique identifier for the operation
            agent_role: Role of the agent that performed the operation
            operation: Name of the operation performed
            success: Whether the operation was successful
            error: Error message if operation failed
            request_size: Size of the request in bytes
            response_size: Size of the response in bytes
        """
        if operation_id not in self.start_times:
            self.logger.warning(f"Operation {operation_id} was not started or already ended")
            return
        
        duration = time.time() - self.start_times[operation_id]
        del self.start_times[operation_id]
        
        # Get system metrics
        try:
            process = psutil.Process()
            memory_usage = process.memory_info().rss / 1024 / 1024  # MB
            cpu_usage = process.cpu_percent()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            memory_usage = 0.0
            cpu_usage = 0.0
            self.logger.warning("Could not retrieve system metrics")
        
        metrics = PerformanceMetrics(
            agent_role=agent_role,
            operation=operation,
            duration=duration,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage,
            timestamp=datetime.now(),
            success=success,
            error_message=error,
            request_size=request_size,
            response_size=response_size
        )
        
        # Add metrics and manage memory
        self.metrics.append(metrics)
        if len(self.metrics) > self._max_metrics:
            # Remove oldest metrics to prevent memory issues
            self.metrics = self.metrics[-self._max_metrics//2:]
            self.logger.info(f"Trimmed metrics to {len(self.metrics)} entries")
        
        # Log metrics
        status = "SUCCESS" if success else "FAILED"
        self.logger.info(
            f"Operation {operation} [{status}] completed in {duration:.2f}s "
            f"(Memory: {memory_usage:.1f}MB, CPU: {cpu_usage:.1f}%)"
        )
        
        if error:
            self.logger.error(f"Operation {operation} failed: {error}")
    
    def get_metrics_summary(self, agent_role: str = None, hours: int = None) -> Dict[str, Any]:
        """Get summary of performance metrics.
        
        Args:
            agent_role: Filter metrics by agent role
            hours: Only include metrics from the last N hours
            
        Returns:
            Dictionary containing performance summary
        """
        filtered_metrics = self.metrics
        
        # Filter by agent role
        if agent_role:
            filtered_metrics = [m for m in filtered_metrics if m.agent_role == agent_role]
        
        # Filter by time window
        if hours:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            filtered_metrics = [m for m in filtered_metrics if m.timestamp >= cutoff_time]
        
        if not filtered_metrics:
            return {
                "total_operations": 0,
                "successful_operations": 0,
                "success_rate": 0.0,
                "average_duration": 0.0,
                "average_memory_usage": 0.0,
                "last_updated": datetime.now().isoformat()
            }
        
        total_operations = len(filtered_metrics)
        successful_operations = len([m for m in filtered_metrics if m.success])
        avg_duration = sum(m.duration for m in filtered_metrics) / total_operations
        avg_memory = sum(m.memory_usage for m in filtered_metrics) / total_operations
        avg_cpu = sum(m.cpu_usage for m in filtered_metrics) / total_operations
        
        # Calculate percentiles for duration
        durations = sorted([m.duration for m in filtered_metrics])
        p50_duration = durations[len(durations) // 2] if durations else 0
        p95_duration = durations[int(len(durations) * 0.95)] if durations else 0
        p99_duration = durations[int(len(durations) * 0.99)] if durations else 0
        
        # Get operation breakdown
        operation_counts = {}
        for metric in filtered_metrics:
            key = f"{metric.agent_role}.{metric.operation}"
            if key not in operation_counts:
                operation_counts[key] = {"count": 0, "success": 0, "avg_duration": 0}
            operation_counts[key]["count"] += 1
            if metric.success:
                operation_counts[key]["success"] += 1
        
        # Calculate average duration per operation
        for key in operation_counts:
            matching_metrics = [
                m for m in filtered_metrics 
                if f"{m.agent_role}.{m.operation}" == key
            ]
            operation_counts[key]["avg_duration"] = (
                sum(m.duration for m in matching_metrics) / len(matching_metrics)
            )
        
        return {
            "total_operations": total_operations,
            "successful_operations": successful_operations,
            "success_rate": successful_operations / total_operations,
            "average_duration": avg_duration,
            "average_memory_usage": avg_memory,
            "average_cpu_usage": avg_cpu,
            "duration_percentiles": {
                "p50": p50_duration,
                "p95": p95_duration,
                "p99": p99_duration
            },
            "operation_breakdown": operation_counts,
            "time_window": f"last {hours} hours" if hours else "all time",
            "agent_filter": agent_role or "all agents",
            "last_updated": datetime.now().isoformat()
        }
    
    def get_recent_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent error metrics.
        
        Args:
            limit: Maximum number of errors to return
            
        Returns:
            List of recent error metrics
        """
        error_metrics = [
            m for m in self.metrics 
            if not m.success and m.error_message
        ]
        
        # Sort by timestamp (most recent first)
        error_metrics.sort(key=lambda x: x.timestamp, reverse=True)
        
        return [
            {
                "timestamp": m.timestamp.isoformat(),
                "agent_role": m.agent_role,
                "operation": m.operation,
                "duration": m.duration,
                "error_message": m.error_message
            }
            for m in error_metrics[:limit]
        ]
    
    def export_metrics(self, filename: str, format: str = "json") -> None:
        """Export metrics to file.
        
        Args:
            filename: Output filename
            format: Export format ('json' or 'csv')
        """
        if format.lower() == "json":
            self._export_json(filename)
        elif format.lower() == "csv":
            self._export_csv(filename)
        else:
            raise ValueError(f"Unsupported export format: {format}")
        
        self.logger.info(f"Exported {len(self.metrics)} metrics to {filename}")
    
    def _export_json(self, filename: str) -> None:
        """Export metrics to JSON file."""
        metrics_data = [
            {
                "agent_role": m.agent_role,
                "operation": m.operation,
                "duration": m.duration,
                "memory_usage": m.memory_usage,
                "cpu_usage": m.cpu_usage,
                "timestamp": m.timestamp.isoformat(),
                "success": m.success,
                "error_message": m.error_message,
                "request_size": m.request_size,
                "response_size": m.response_size
            }
            for m in self.metrics
        ]
        
        with open(filename, 'w') as f:
            json.dump({
                "export_timestamp": datetime.now().isoformat(),
                "total_metrics": len(metrics_data),
                "metrics": metrics_data
            }, f, indent=2)
    
    def _export_csv(self, filename: str) -> None:
        """Export metrics to CSV file."""
        import csv
        
        with open(filename, 'w', newline='') as f:
            writer = csv.writer(f)
            
            # Write header
            writer.writerow([
                'timestamp', 'agent_role', 'operation', 'duration',
                'memory_usage', 'cpu_usage', 'success', 'error_message',
                'request_size', 'response_size'
            ])
            
            # Write data
            for m in self.metrics:
                writer.writerow([
                    m.timestamp.isoformat(),
                    m.agent_role,
                    m.operation,
                    m.duration,
                    m.memory_usage,
                    m.cpu_usage,
                    m.success,
                    m.error_message or '',
                    m.request_size or '',
                    m.response_size or ''
                ])
    
    def clear_metrics(self, older_than_hours: int = None) -> int:
        """Clear metrics from memory.
        
        Args:
            older_than_hours: Only clear metrics older than N hours
            
        Returns:
            Number of metrics cleared
        """
        initial_count = len(self.metrics)
        
        if older_than_hours:
            from datetime import timedelta
            cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
            self.metrics = [m for m in self.metrics if m.timestamp >= cutoff_time]
        else:
            self.metrics.clear()
        
        cleared_count = initial_count - len(self.metrics)
        self.logger.info(f"Cleared {cleared_count} metrics from memory")
        return cleared_count
    
    def get_agent_performance_ranking(self) -> List[Dict[str, Any]]:
        """Get performance ranking of agents.
        
        Returns:
            List of agents ranked by performance metrics
        """
        agent_stats = {}
        
        for metric in self.metrics:
            role = metric.agent_role
            if role not in agent_stats:
                agent_stats[role] = {
                    "total_operations": 0,
                    "successful_operations": 0,
                    "total_duration": 0.0,
                    "total_memory": 0.0
                }
            
            stats = agent_stats[role]
            stats["total_operations"] += 1
            stats["total_duration"] += metric.duration
            stats["total_memory"] += metric.memory_usage
            
            if metric.success:
                stats["successful_operations"] += 1
        
        # Calculate derived metrics and create ranking
        ranking = []
        for role, stats in agent_stats.items():
            if stats["total_operations"] > 0:
                success_rate = stats["successful_operations"] / stats["total_operations"]
                avg_duration = stats["total_duration"] / stats["total_operations"]
                avg_memory = stats["total_memory"] / stats["total_operations"]
                
                # Performance score (higher is better)
                # Weighted combination of success rate and inverse of duration
                performance_score = (success_rate * 0.7) + ((1 / (avg_duration + 0.1)) * 0.3)
                
                ranking.append({
                    "agent_role": role,
                    "total_operations": stats["total_operations"],
                    "success_rate": success_rate,
                    "average_duration": avg_duration,
                    "average_memory_usage": avg_memory,
                    "performance_score": performance_score
                })
        
        # Sort by performance score (descending)
        ranking.sort(key=lambda x: x["performance_score"], reverse=True)
        return ranking
    
    def export_to_json(self, filepath: str = None, agent_role: str = None, hours: int = None) -> str:
        """Export performance metrics to JSON format.
        
        Args:
            filepath: Optional file path to save JSON data
            agent_role: Filter metrics by agent role
            hours: Only include metrics from the last N hours
            
        Returns:
            JSON string containing the metrics data
        """
        filtered_metrics = self.metrics
        
        # Filter by agent role
        if agent_role:
            filtered_metrics = [m for m in filtered_metrics if m.agent_role == agent_role]
        
        # Filter by time window
        if hours:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            filtered_metrics = [m for m in filtered_metrics if m.timestamp >= cutoff_time]
        
        # Convert metrics to JSON-serializable format
        metrics_data = []
        for metric in filtered_metrics:
            metrics_data.append({
                "agent_role": metric.agent_role,
                "operation": metric.operation,
                "duration": metric.duration,
                "memory_usage": metric.memory_usage,
                "cpu_usage": metric.cpu_usage,
                "timestamp": metric.timestamp.isoformat(),
                "success": metric.success,
                "error_message": metric.error_message,
                "request_size": metric.request_size,
                "response_size": metric.response_size
            })
        
        # Create export data structure
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "total_metrics": len(metrics_data),
            "filters": {
                "agent_role": agent_role,
                "hours": hours
            },
            "summary": self.get_metrics_summary(agent_role, hours),
            "metrics": metrics_data
        }
        
        # Convert to JSON string
        json_data = json.dumps(export_data, indent=2, default=str)
        
        # Save to file if filepath provided
        if filepath:
            try:
                with open(filepath, 'w') as f:
                    f.write(json_data)
                self.logger.info(f"Metrics exported to {filepath}")
            except Exception as e:
                self.logger.error(f"Failed to export metrics to {filepath}: {e}")
                raise
        
        return json_data

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

# Decorator for automatic performance monitoring
def monitor_performance(operation_name: str = None):
    """Decorator to automatically monitor method performance.
    
    Args:
        operation_name: Custom name for the operation (defaults to method name)
    """
    def decorator(func):
        import functools
        import uuid
        
        @functools.wraps(func)
        async def async_wrapper(self, *args, **kwargs):
            operation_id = str(uuid.uuid4())
            op_name = operation_name or func.__name__
            agent_role = getattr(self, 'role', 'unknown')
            
            performance_monitor.start_operation(operation_id)
            
            try:
                result = await func(self, *args, **kwargs)
                performance_monitor.end_operation(
                    operation_id, agent_role, op_name, success=True
                )
                return result
            except Exception as e:
                performance_monitor.end_operation(
                    operation_id, agent_role, op_name, 
                    success=False, error=str(e)
                )
                raise
        
        @functools.wraps(func)
        def sync_wrapper(self, *args, **kwargs):
            operation_id = str(uuid.uuid4())
            op_name = operation_name or func.__name__
            agent_role = getattr(self, 'role', 'unknown')
            
            performance_monitor.start_operation(operation_id)
            
            try:
                result = func(self, *args, **kwargs)
                performance_monitor.end_operation(
                    operation_id, agent_role, op_name, success=True
                )
                return result
            except Exception as e:
                performance_monitor.end_operation(
                    operation_id, agent_role, op_name, 
                    success=False, error=str(e)
                )
                raise
        
        # Return appropriate wrapper based on whether function is async
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator