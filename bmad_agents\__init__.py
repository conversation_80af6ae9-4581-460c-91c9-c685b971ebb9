"""BMad Pydantic AI Agents - A comprehensive multi-agent system for brownfield application development.

This package provides a suite of AI agents specialized in different aspects of software development,
including analysis, architecture, development, testing, and deployment. The agents work together
through an orchestrator to handle complex brownfield development tasks.

Key Components:
- BMadOrchestrator: Central coordinator for routing requests to appropriate agents
- Individual Agents: Specialized agents for different development tasks
- Workflows: Pre-defined sequences of agent interactions for common scenarios
- Configuration: Centralized configuration management
- Monitoring: Performance tracking and metrics collection
- Error Handling: Production-ready error handling with retry and circuit breaker patterns

Example:
    >>> from bmad_agents import BMadOrchestrator, config
    >>> orchestrator = BMadOrchestrator()
    >>> response = await orchestrator.route_request("Analyze this codebase")
"""

__version__ = "1.0.0"

# Import base infrastructure
from .base import (
    BMadAgent,
    AgentMessage,
    AgentRequest,
    AgentResponse,
    WorkflowState,
    WorkflowStep,
    MessageType,
    Priority,
    StateManager,
    BMadConfig,
    AgentConfig,
    WorkflowConfig,
    setup_logging,
    get_logger,
    BMadLogger
)

# Import error handling and monitoring
from .base.error_handling import (
    <PERSON><PERSON>ad<PERSON><PERSON><PERSON>,
    AgentTimeoutError,
    AgentExecutionError,
    WorkflowError,
    with_retry,
    with_timeout,
    CircuitBreaker
)

from .base.monitoring import (
    PerformanceMetrics,
    PerformanceMonitor,
    performance_monitor
)

# Core orchestrator
from .agents.orchestrator import BMadOrchestrator

# Individual agents
from .agents.analyst import AnalystAgent
from .agents.architect import ArchitectAgent

# Workflows
from .workflows.brownfield_fullstack import BrownfieldFullstackWorkflow

# Configuration and monitoring
from .base.config import config
from .base.monitoring import performance_monitor, PerformanceMetrics, PerformanceMonitor

# Version and metadata
__version__ = '1.0.0'
__author__ = 'BMad Method Team'
__description__ = 'BMad Method AI agents implementation using Pydantic AI'
__license__ = 'MIT'

# Package-level exports
__all__ = [
    # Version and metadata
    '__version__',
    
    # Core orchestrator
    'BMadOrchestrator',
    
    # Individual agents
    'AnalystAgent',
    'ArchitectAgent',
    
    # Workflows
    'BrownfieldFullstackWorkflow',
    
    # Base classes
    'BMadAgent',
    'StateManager', 
    'BMadConfig',
    'config',
    'setup_logging',
    
    # Communication models
    'AgentMessage',
    'AgentRequest',
    'AgentResponse',
    'WorkflowState', 
    'WorkflowStep',
    'MessageType',
    'Priority',
    
    # Configuration models
    'AgentConfig',
    'WorkflowConfig',
    
    # Monitoring
    'performance_monitor',
    'PerformanceMetrics',
    'PerformanceMonitor',
    
    # Error handling
    'BMadError',
    'AgentTimeoutError',
    'AgentExecutionError',
    'WorkflowError',
    'with_retry',
    'with_timeout',
    'CircuitBreaker',
    
    # Logging utilities
    'get_logger',
    'BMadLogger'
]

# Initialize logging when package is imported
_logger = None

def initialize_bmad_system(log_level: str = "INFO", log_dir: str = "logs") -> BMadLogger:
    """Initialize the BMad agents system with logging and configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_dir: Directory for log files
        
    Returns:
        BMadLogger: Configured logger instance
    """
    global _logger
    if _logger is None:
        _logger = setup_logging(log_dir=log_dir, log_level=log_level)
        _logger.get_system_logger("init").info("BMad agents system initialized")
    return _logger

def get_system_logger():
    """Get the system logger instance."""
    if _logger is None:
        initialize_bmad_system()
    return _logger