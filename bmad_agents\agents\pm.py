from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from ..base.bmad_agent import BMadAgent

class ProjectTask(BaseModel):
    task_id: str
    name: str
    description: str
    assignee: Optional[str] = None
    estimated_hours: float
    start_date: Optional[date] = None
    due_date: Optional[date] = None
    dependencies: List[str] = Field(default_factory=list)
    status: str = "not_started"  # not_started, in_progress, completed, blocked
    priority: str = "medium"  # low, medium, high, critical

class ProjectMilestone(BaseModel):
    milestone_id: str
    name: str
    description: str
    target_date: date
    deliverables: List[str]
    success_criteria: List[str]
    dependencies: List[str] = Field(default_factory=list)

class ResourceAllocation(BaseModel):
    resource_name: str
    role: str
    availability_percentage: float
    skills: List[str]
    assigned_tasks: List[str] = Field(default_factory=list)
    workload_hours: float = 0.0

class RiskAssessment(BaseModel):
    risk_id: str
    description: str
    probability: str  # low, medium, high
    impact: str  # low, medium, high
    mitigation_strategy: str
    owner: Optional[str] = None
    status: str = "identified"  # identified, mitigating, resolved

class ProjectPlan(BaseModel):
    project_name: str
    description: str
    objectives: List[str]
    scope: str
    tasks: List[ProjectTask]
    milestones: List[ProjectMilestone]
    resources: List[ResourceAllocation]
    risks: List[RiskAssessment]
    timeline_weeks: int
    budget_estimate: Optional[float] = None
    success_metrics: List[str]

class ProjectStatus(BaseModel):
    overall_progress: float = Field(ge=0, le=100)
    completed_tasks: int
    total_tasks: int
    upcoming_milestones: List[str]
    current_risks: List[str]
    resource_utilization: Dict[str, float]
    schedule_variance: str  # ahead, on_track, behind
    budget_variance: Optional[str] = None
    recommendations: List[str]

class PMAgent(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are an expert Project Manager in the BMad Method framework.
        
        Your responsibilities include:
        - Creating comprehensive project plans with tasks, milestones, and timelines
        - Managing resource allocation and workload distribution
        - Identifying and mitigating project risks
        - Tracking project progress and providing status updates
        - Ensuring project delivery within scope, time, and budget constraints
        
        Always provide realistic timelines and resource estimates.
        Focus on clear communication, risk management, and stakeholder alignment.
        Consider dependencies, resource constraints, and potential bottlenecks.
        """
        
        super().__init__(
            role="pm",
            system_prompt=system_prompt,
            response_model=ProjectPlan
        )
    
    async def create_project_plan(self, requirements: str, constraints: Dict[str, Any] = None) -> ProjectPlan:
        """Create a comprehensive project plan based on requirements."""
        prompt = f"""
        Create a detailed project plan for the following requirements:
        
        Requirements:
        {requirements}
        
        {f"Constraints: {constraints}" if constraints else ""}
        
        Provide a comprehensive project plan including:
        - Clear project objectives and scope definition
        - Detailed task breakdown with estimates and dependencies
        - Key milestones with deliverables and success criteria
        - Resource allocation and skill requirements
        - Risk assessment with mitigation strategies
        - Realistic timeline and budget estimates
        - Success metrics and KPIs
        """
        
        result = await self.run(prompt)
        return result
    
    async def track_progress(self, project_plan: ProjectPlan, current_status: Dict[str, Any]) -> ProjectStatus:
        """Track project progress and provide status update."""
        prompt = f"""
        Analyze the current project status against the original plan:
        
        Original Plan:
        - Project: {project_plan.project_name}
        - Total Tasks: {len(project_plan.tasks)}
        - Milestones: {len(project_plan.milestones)}
        - Timeline: {project_plan.timeline_weeks} weeks
        
        Current Status:
        {current_status}
        
        Provide a comprehensive status update including:
        - Overall progress percentage
        - Task completion statistics
        - Upcoming milestones and deadlines
        - Current risks and issues
        - Resource utilization analysis
        - Schedule and budget variance
        - Actionable recommendations
        """
        
        temp_agent = BMadAgent(
            role="pm-tracking",
            system_prompt=self.system_prompt,
            response_model=ProjectStatus
        )
        
        return await temp_agent.run(prompt)
    
    async def allocate_resources(self, tasks: List[ProjectTask], available_resources: List[ResourceAllocation]) -> List[ResourceAllocation]:
        """Optimize resource allocation across project tasks."""
        tasks_summary = "\n".join([f"- {task.name}: {task.estimated_hours}h, skills needed: {task.description}" for task in tasks])
        resources_summary = "\n".join([f"- {res.resource_name} ({res.role}): {res.availability_percentage}% available, skills: {', '.join(res.skills)}" for res in available_resources])
        
        prompt = f"""
        Optimize resource allocation for the following tasks and available resources:
        
        Tasks:
        {tasks_summary}
        
        Available Resources:
        {resources_summary}
        
        Provide optimized resource allocation considering:
        - Skill matching and expertise levels
        - Availability and workload balancing
        - Task dependencies and critical path
        - Resource utilization efficiency
        - Risk mitigation through backup assignments
        
        Return updated resource allocations with assigned tasks and workload hours.
        """
        
        class ResourceAllocationList(BaseModel):
            allocations: List[ResourceAllocation]
        
        temp_agent = BMadAgent(
            role="pm-allocation",
            system_prompt=self.system_prompt,
            response_model=ResourceAllocationList
        )
        
        result = await temp_agent.run(prompt)
        return result.allocations
    
    async def assess_risks(self, project_context: str, existing_risks: List[RiskAssessment] = None) -> List[RiskAssessment]:
        """Identify and assess project risks with mitigation strategies."""
        existing_risks_text = ""
        if existing_risks:
            existing_risks_text = "\n".join([f"- {risk.description} ({risk.probability}/{risk.impact})" for risk in existing_risks])
        
        prompt = f"""
        Identify and assess risks for the following project context:
        
        Project Context:
        {project_context}
        
        {f"Existing Risks: {existing_risks_text}" if existing_risks_text else ""}
        
        Provide comprehensive risk assessment including:
        - Technical risks (architecture, technology, integration)
        - Resource risks (availability, skills, dependencies)
        - Schedule risks (dependencies, estimates, external factors)
        - Business risks (requirements changes, stakeholder alignment)
        - External risks (market, regulatory, vendor dependencies)
        
        For each risk, provide:
        - Clear description and impact
        - Probability and impact assessment (low/medium/high)
        - Specific mitigation strategies
        - Recommended risk owner
        """
        
        class RiskAssessmentList(BaseModel):
            risks: List[RiskAssessment]
        
        temp_agent = BMadAgent(
            role="pm-risks",
            system_prompt=self.system_prompt,
            response_model=RiskAssessmentList
        )
        
        result = await temp_agent.run(prompt)
        return result.risks
    
    async def create_timeline(self, tasks: List[ProjectTask], milestones: List[ProjectMilestone]) -> Dict[str, Any]:
        """Create detailed project timeline with critical path analysis."""
        tasks_summary = "\n".join([f"- {task.name}: {task.estimated_hours}h, deps: {', '.join(task.dependencies)}" for task in tasks])
        milestones_summary = "\n".join([f"- {ms.name}: {ms.target_date}, deps: {', '.join(ms.dependencies)}" for ms in milestones])
        
        prompt = f"""
        Create a detailed project timeline with critical path analysis:
        
        Tasks:
        {tasks_summary}
        
        Milestones:
        {milestones_summary}
        
        Provide timeline analysis including:
        - Critical path identification
        - Task scheduling with dependencies
        - Milestone alignment and feasibility
        - Buffer time recommendations
        - Resource leveling considerations
        - Timeline optimization suggestions
        """
        
        class TimelineAnalysis(BaseModel):
            critical_path: List[str]
            total_duration_weeks: int
            milestone_schedule: Dict[str, str]
            resource_peaks: List[str]
            buffer_recommendations: List[str]
            optimization_suggestions: List[str]
        
        temp_agent = BMadAgent(
            role="pm-timeline",
            system_prompt=self.system_prompt,
            response_model=TimelineAnalysis
        )
        
        result = await temp_agent.run(prompt)
        return result.model_dump()