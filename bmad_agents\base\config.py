import os
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from pathlib import Path
import yaml
import json
from dotenv import load_dotenv
import logging

class AgentConfig(BaseModel):
    """Configuration for individual agents."""
    role: str = Field(description="Agent role name")
    model: str = Field(default="gemini-2.5-flash", description="AI model to use")
    max_tokens: Optional[int] = Field(default=None, description="Maximum tokens per response")
    temperature: float = Field(default=0.7, description="Model temperature")
    timeout: int = Field(default=30, description="Request timeout in seconds")
    retry_attempts: int = Field(default=3, description="Number of retry attempts")
    system_prompt_template: Optional[str] = Field(default=None, description="Custom system prompt template")
    tools: List[str] = Field(default_factory=list, description="Available tools for this agent")
    capabilities: List[str] = Field(default_factory=list, description="Agent capabilities")
    dependencies: List[str] = Field(default_factory=list, description="Required other agents")

class WorkflowConfig(BaseModel):
    """Configuration for workflows."""
    name: str = Field(description="Workflow name")
    description: str = Field(description="Workflow description")
    steps: List[Dict[str, Any]] = Field(description="Workflow steps configuration")
    default_timeout: int = Field(default=300, description="Default step timeout in seconds")
    parallel_execution: bool = Field(default=False, description="Allow parallel step execution")
    error_handling: str = Field(default="stop", description="Error handling strategy")

class BMadConfig:
    """
    Central configuration manager for BMad agents system.
    Handles loading configuration from files, environment variables, and defaults.
    """
    
    def __init__(self, config_dir: str = ".bmad-core"):
        self.config_dir = Path(config_dir)
        self.logger = logging.getLogger("bmad.config")
        
        # Load environment variables
        load_dotenv()
        
        # Initialize configuration
        self._agents_config: Dict[str, AgentConfig] = {}
        self._workflows_config: Dict[str, WorkflowConfig] = {}
        self._global_config: Dict[str, Any] = {}
        
        # Load configurations
        self._load_global_config()
        self._load_agents_config()
        self._load_workflows_config()
        
        self.logger.info("BMadConfig initialized successfully")
    
    def _load_global_config(self):
        """Load global configuration settings."""
        try:
            # Default global configuration
            self._global_config = {
                'default_model': os.getenv('BMAD_DEFAULT_MODEL', 'gemini-2.5-flash'),
                'api_keys': {
                    'google_ai': os.getenv('GOOGLE_AI_API_KEY'),
                    'openai': os.getenv('OPENAI_API_KEY'),
                    'anthropic': os.getenv('ANTHROPIC_API_KEY')
                },
                'logging': {
                    'level': os.getenv('BMAD_LOG_LEVEL', 'INFO'),
                    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                },
                'state_management': {
                    'enabled': os.getenv('BMAD_STATE_ENABLED', 'true').lower() == 'true',
                    'state_dir': os.getenv('BMAD_STATE_DIR', '.bmad_state'),
                    'cleanup_days': int(os.getenv('BMAD_CLEANUP_DAYS', '30'))
                },
                'performance': {
                    'max_concurrent_agents': int(os.getenv('BMAD_MAX_CONCURRENT', '5')),
                    'default_timeout': int(os.getenv('BMAD_DEFAULT_TIMEOUT', '30')),
                    'retry_attempts': int(os.getenv('BMAD_RETRY_ATTEMPTS', '3'))
                }
            }
            
            # Try to load from core-config.yaml if it exists
            core_config_file = self.config_dir / 'core-config.yaml'
            if core_config_file.exists():
                with open(core_config_file, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f)
                    if file_config:
                        self._global_config.update(file_config)
                        self.logger.info("Loaded global config from core-config.yaml")
            
        except Exception as e:
            self.logger.error(f"Failed to load global config: {str(e)}")
    
    def _load_agents_config(self):
        """Load agent configurations from files."""
        try:
            agents_dir = self.config_dir / 'agents'
            if not agents_dir.exists():
                self.logger.warning(f"Agents config directory not found: {agents_dir}")
                return
            
            # Load each agent configuration
            for agent_file in agents_dir.glob('*.md'):
                agent_role = agent_file.stem
                
                # Create default config for each agent
                agent_config = AgentConfig(
                    role=agent_role,
                    model=self._global_config.get('default_model', 'gemini-2.5-flash'),
                    capabilities=[f"{agent_role}_tasks"],
                    tools=[]
                )
                
                self._agents_config[agent_role] = agent_config
                self.logger.debug(f"Loaded config for agent: {agent_role}")
            
            # Load team configurations
            teams_dir = self.config_dir / 'agent-teams'
            if teams_dir.exists():
                for team_file in teams_dir.glob('*.yaml'):
                    try:
                        with open(team_file, 'r', encoding='utf-8') as f:
                            team_config = yaml.safe_load(f)
                            # Process team configuration if needed
                            self.logger.debug(f"Loaded team config: {team_file.stem}")
                    except Exception as e:
                        self.logger.warning(f"Failed to load team config {team_file}: {str(e)}")
            
        except Exception as e:
            self.logger.error(f"Failed to load agents config: {str(e)}")
    
    def _load_workflows_config(self):
        """Load workflow configurations from files."""
        try:
            workflows_dir = self.config_dir / 'workflows'
            if not workflows_dir.exists():
                self.logger.warning(f"Workflows config directory not found: {workflows_dir}")
                return
            
            for workflow_file in workflows_dir.glob('*.yaml'):
                try:
                    with open(workflow_file, 'r', encoding='utf-8') as f:
                        workflow_data = yaml.safe_load(f)
                    
                    if workflow_data:
                        workflow_name = workflow_file.stem
                        workflow_config = WorkflowConfig(
                            name=workflow_name,
                            description=workflow_data.get('description', f'{workflow_name} workflow'),
                            steps=workflow_data.get('steps', []),
                            default_timeout=workflow_data.get('default_timeout', 300),
                            parallel_execution=workflow_data.get('parallel_execution', False),
                            error_handling=workflow_data.get('error_handling', 'stop')
                        )
                        
                        self._workflows_config[workflow_name] = workflow_config
                        self.logger.debug(f"Loaded workflow config: {workflow_name}")
                
                except Exception as e:
                    self.logger.warning(f"Failed to load workflow {workflow_file}: {str(e)}")
        
        except Exception as e:
            self.logger.error(f"Failed to load workflows config: {str(e)}")
    
    def get_agent_config(self, role: str) -> Optional[AgentConfig]:
        """Get configuration for a specific agent role."""
        return self._agents_config.get(role)
    
    def get_workflow_config(self, name: str) -> Optional[WorkflowConfig]:
        """Get configuration for a specific workflow."""
        return self._workflows_config.get(name)
    
    def get_global_config(self, key: str = None) -> Any:
        """Get global configuration value(s)."""
        if key:
            return self._global_config.get(key)
        return self._global_config
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """Get API key for a specific provider."""
        api_keys = self._global_config.get('api_keys', {})
        return api_keys.get(provider)
    
    def list_available_agents(self) -> List[str]:
        """List all available agent roles."""
        return list(self._agents_config.keys())
    
    def list_available_workflows(self) -> List[str]:
        """List all available workflows."""
        return list(self._workflows_config.keys())
    
    def update_agent_config(self, role: str, config_updates: Dict[str, Any]) -> bool:
        """Update configuration for a specific agent."""
        try:
            if role in self._agents_config:
                current_config = self._agents_config[role]
                updated_data = current_config.model_dump()
                updated_data.update(config_updates)
                
                self._agents_config[role] = AgentConfig(**updated_data)
                self.logger.info(f"Updated config for agent: {role}")
                return True
            else:
                self.logger.warning(f"Agent role not found: {role}")
                return False
        
        except Exception as e:
            self.logger.error(f"Failed to update agent config for {role}: {str(e)}")
            return False
    
    def validate_configuration(self) -> Dict[str, List[str]]:
        """Validate the current configuration and return any issues."""
        issues = {
            'errors': [],
            'warnings': []
        }
        
        # Check API keys
        api_keys = self._global_config.get('api_keys', {})
        if not any(api_keys.values()):
            issues['errors'].append("No API keys configured")
        
        # Check agent configurations
        if not self._agents_config:
            issues['warnings'].append("No agent configurations loaded")
        
        # Check workflow configurations
        if not self._workflows_config:
            issues['warnings'].append("No workflow configurations loaded")
        
        # Validate agent dependencies
        for role, config in self._agents_config.items():
            for dep in config.dependencies:
                if dep not in self._agents_config:
                    issues['errors'].append(f"Agent {role} depends on missing agent: {dep}")
        
        return issues

# Global configuration instance
config = BMadConfig()