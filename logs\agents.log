2025-08-03 14:04:01,856 - bmad.config - INFO - [config.py:91] - Loaded global config from core-config.yaml
2025-08-03 14:04:01,890 - bmad.config - INFO - [config.py:55] - BMadConfig initialized successfully
2025-08-03 14:04:01,891 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 14:04:01,893 - bmad.state_manager - INFO - [state_manager.py:40] - Workflow state saved: test-workflow-001
2025-08-03 14:04:01,894 - bmad.state_manager - INFO - [state_manager.py:68] - Workflow state loaded: test-workflow-001
2025-08-03 14:04:04,146 - bmad.state_manager - INFO - [state_manager.py:96] - Workflow state deleted: test-workflow-001
2025-08-03 14:19:38,714 - bmad.config - INFO - [config.py:91] - Loaded global config from core-config.yaml
2025-08-03 14:19:38,746 - bmad.config - INFO - [config.py:55] - BMadConfig initialized successfully
2025-08-03 14:20:02,031 - bmad.config - INFO - [config.py:91] - Loaded global config from core-config.yaml
2025-08-03 14:20:02,061 - bmad.config - INFO - [config.py:55] - BMadConfig initialized successfully
2025-08-03 14:20:02,062 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 14:20:02,064 - bmad.state_manager - INFO - [state_manager.py:40] - Workflow state saved: test-workflow-001
2025-08-03 14:20:02,065 - bmad.state_manager - INFO - [state_manager.py:68] - Workflow state loaded: test-workflow-001
2025-08-03 14:20:04,241 - bmad.state_manager - INFO - [state_manager.py:96] - Workflow state deleted: test-workflow-001
2025-08-03 14:20:06,962 - bmad.config - INFO - [config.py:91] - Loaded global config from core-config.yaml
2025-08-03 14:20:06,992 - bmad.config - INFO - [config.py:55] - BMadConfig initialized successfully
2025-08-03 14:20:07,747 - bmad.test-analyst - INFO - [bmad_agent.py:47] - Processing request: analysis
2025-08-03 14:20:07,747 - bmad.test-analyst - ERROR - [bmad_agent.py:65] - Error processing request: 'BMadAgent' object has no attribute 'arun'
2025-08-03 14:20:28,740 - bmad.config - INFO - [config.py:91] - Loaded global config from core-config.yaml
2025-08-03 14:20:28,773 - bmad.config - INFO - [config.py:55] - BMadConfig initialized successfully
2025-08-03 14:20:28,774 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 14:20:28,776 - bmad.state_manager - INFO - [state_manager.py:40] - Workflow state saved: test-workflow-001
2025-08-03 14:20:28,777 - bmad.state_manager - INFO - [state_manager.py:68] - Workflow state loaded: test-workflow-001
2025-08-03 14:20:30,976 - bmad.state_manager - INFO - [state_manager.py:96] - Workflow state deleted: test-workflow-001
2025-08-03 14:20:30,986 - bmad.config - INFO - [config.py:91] - Loaded global config from core-config.yaml
2025-08-03 14:20:31,018 - bmad.config - INFO - [config.py:55] - BMadConfig initialized successfully
2025-08-03 14:20:31,770 - bmad.test-analyst - INFO - [bmad_agent.py:47] - Processing request: analysis
2025-08-03 14:20:33,443 - bmad.test-analyst - ERROR - [bmad_agent.py:65] - Error processing request: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'An internal error occurred. (Details: go/rwhop.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.)', 'status': 'INVALID_ARGUMENT'}}
2025-08-03 14:21:15,961 - bmad.config - INFO - [config.py:91] - Loaded global config from core-config.yaml
2025-08-03 14:21:15,997 - bmad.config - INFO - [config.py:55] - BMadConfig initialized successfully
2025-08-03 14:21:15,999 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 14:21:18,213 - bmad.state_manager - INFO - [state_manager.py:40] - Workflow state saved: demo-workflow-001
2025-08-03 14:21:18,214 - bmad.state_manager - INFO - [state_manager.py:68] - Workflow state loaded: demo-workflow-001
2025-08-03 14:21:44,872 - bmad.config - INFO - [config.py:91] - Loaded global config from core-config.yaml
2025-08-03 14:21:44,903 - bmad.config - INFO - [config.py:55] - BMadConfig initialized successfully
2025-08-03 14:21:44,904 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 14:21:47,076 - bmad.state_manager - INFO - [state_manager.py:40] - Workflow state saved: demo-workflow-001
2025-08-03 14:21:47,077 - bmad.state_manager - INFO - [state_manager.py:68] - Workflow state loaded: demo-workflow-001
2025-08-03 14:21:47,078 - bmad.state_manager - INFO - [state_manager.py:96] - Workflow state deleted: demo-workflow-001
2025-08-03 15:39:15,902 - bmad.example - INFO - [complete_example.py:377] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 15:39:15,902 - bmad.example - INFO - [complete_example.py:378] - ============================================================
2025-08-03 15:39:15,902 - bmad.example - INFO - [complete_example.py:55] - Initializing BMad Pydantic AI Agents system...
2025-08-03 15:39:18,109 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:39:18,109 - bmad.example - ERROR - [complete_example.py:402] - Example execution failed: 'BMadConfig' object has no attribute 'update'
2025-08-03 15:39:58,425 - bmad.example - INFO - [complete_example.py:377] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 15:39:58,426 - bmad.example - INFO - [complete_example.py:378] - ============================================================
2025-08-03 15:39:58,426 - bmad.example - INFO - [complete_example.py:55] - Initializing BMad Pydantic AI Agents system...
2025-08-03 15:40:00,620 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:40:00,620 - bmad.example - INFO - [complete_example.py:68] - System initialized successfully
2025-08-03 15:40:00,620 - bmad.example - INFO - [complete_example.py:357] - 
=== System Information ===
2025-08-03 15:40:00,620 - bmad.example - INFO - [complete_example.py:360] - Configuration:
2025-08-03 15:40:00,620 - bmad.example - INFO - [complete_example.py:362] -   default_model: gemini-2.0-flash-exp
2025-08-03 15:40:00,621 - bmad.example - INFO - [complete_example.py:362] -   api_keys: {'google_ai': 'AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA', 'openai': '***********************************************************************************************', 'anthropic': '************************************************************************************************************'}
2025-08-03 15:40:00,621 - bmad.example - INFO - [complete_example.py:362] -   logging: {'level': 'INFO', 'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'}
2025-08-03 15:40:00,621 - bmad.example - INFO - [complete_example.py:362] -   state_management: {'enabled': True, 'state_dir': '.bmad_state', 'cleanup_days': 30}
2025-08-03 15:40:00,621 - bmad.example - INFO - [complete_example.py:362] -   performance: {'max_concurrent_agents': 5, 'default_timeout': 30, 'retry_attempts': 3}
2025-08-03 15:40:00,621 - bmad.example - INFO - [complete_example.py:362] -   markdownExploder: True
2025-08-03 15:40:00,621 - bmad.example - INFO - [complete_example.py:362] -   prd: {'prdFile': 'docs/prd.md', 'prdVersion': 'v4', 'prdSharded': True, 'prdShardedLocation': 'docs/prd', 'epicFilePattern': 'epic-{n}*.md'}
2025-08-03 15:40:00,621 - bmad.example - INFO - [complete_example.py:362] -   architecture: {'architectureFile': 'docs/architecture.md', 'architectureVersion': 'v4', 'architectureSharded': True, 'architectureShardedLocation': 'docs/architecture'}
2025-08-03 15:40:00,621 - bmad.example - INFO - [complete_example.py:362] -   customTechnicalDocuments: None
2025-08-03 15:40:00,622 - bmad.example - INFO - [complete_example.py:362] -   devLoadAlwaysFiles: ['docs/architecture/coding-standards.md', 'docs/architecture/tech-stack.md', 'docs/architecture/source-tree.md']
2025-08-03 15:40:00,622 - bmad.example - INFO - [complete_example.py:362] -   devDebugLog: .ai/debug-log.md
2025-08-03 15:40:00,623 - bmad.example - INFO - [complete_example.py:362] -   devStoryLocation: docs/stories
2025-08-03 15:40:00,623 - bmad.example - INFO - [complete_example.py:362] -   slashPrefix: BMad
2025-08-03 15:40:00,623 - bmad.example - INFO - [complete_example.py:362] -   project_path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:40:00,623 - bmad.example - INFO - [complete_example.py:362] -   log_level: INFO
2025-08-03 15:40:00,623 - bmad.example - INFO - [complete_example.py:362] -   enable_monitoring: True
2025-08-03 15:40:00,623 - bmad.example - INFO - [complete_example.py:362] -   max_concurrent_agents: 3
2025-08-03 15:40:00,623 - bmad.example - INFO - [complete_example.py:370] - 
System Status:
2025-08-03 15:40:00,623 - bmad.example - INFO - [complete_example.py:371] -   Project Path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:40:00,624 - bmad.example - INFO - [complete_example.py:372] -   Monitoring Enabled: True
2025-08-03 15:40:00,624 - bmad.example - INFO - [complete_example.py:373] -   Log Level: INFO
2025-08-03 15:40:00,624 - bmad.example - INFO - [complete_example.py:72] - 
=== Demonstrating Orchestrator Request Routing ===
2025-08-03 15:40:00,624 - bmad.example - INFO - [complete_example.py:91] - 
Request 1: Analyze the current codebase structure and identify technical debt
2025-08-03 15:40:00,624 - bmad.example - ERROR - [complete_example.py:115] - Error processing request 1: type object 'MessageType' has no attribute 'QUERY'
2025-08-03 15:40:00,624 - bmad.example - INFO - [complete_example.py:91] - 
Request 2: Design a microservices architecture for this monolithic application
2025-08-03 15:40:00,624 - bmad.example - ERROR - [complete_example.py:115] - Error processing request 2: type object 'MessageType' has no attribute 'QUERY'
2025-08-03 15:40:00,625 - bmad.example - INFO - [complete_example.py:91] - 
Request 3: Help me understand how to use the BMad agents system
2025-08-03 15:40:00,625 - bmad.example - ERROR - [complete_example.py:115] - Error processing request 3: type object 'MessageType' has no attribute 'QUERY'
2025-08-03 15:40:00,625 - bmad.example - INFO - [complete_example.py:119] - 
=== Demonstrating Direct Agent Usage ===
2025-08-03 15:40:00,625 - bmad.example - INFO - [complete_example.py:122] - 
Using AnalystAgent directly:
2025-08-03 15:40:01,377 - bmad.example - ERROR - [complete_example.py:402] - Example execution failed: type object 'MessageType' has no attribute 'ANALYSIS_REQUEST'
2025-08-03 15:40:50,035 - bmad.example - INFO - [complete_example.py:377] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 15:40:50,035 - bmad.example - INFO - [complete_example.py:378] - ============================================================
2025-08-03 15:40:50,036 - bmad.example - INFO - [complete_example.py:55] - Initializing BMad Pydantic AI Agents system...
2025-08-03 15:40:52,253 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:40:52,253 - bmad.example - INFO - [complete_example.py:68] - System initialized successfully
2025-08-03 15:40:52,253 - bmad.example - INFO - [complete_example.py:357] - 
=== System Information ===
2025-08-03 15:40:52,253 - bmad.example - INFO - [complete_example.py:360] - Configuration:
2025-08-03 15:40:52,254 - bmad.example - INFO - [complete_example.py:362] -   default_model: gemini-2.0-flash-exp
2025-08-03 15:40:52,254 - bmad.example - INFO - [complete_example.py:362] -   api_keys: {'google_ai': 'AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA', 'openai': '***********************************************************************************************', 'anthropic': '************************************************************************************************************'}
2025-08-03 15:40:52,254 - bmad.example - INFO - [complete_example.py:362] -   logging: {'level': 'INFO', 'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'}
2025-08-03 15:40:52,254 - bmad.example - INFO - [complete_example.py:362] -   state_management: {'enabled': True, 'state_dir': '.bmad_state', 'cleanup_days': 30}
2025-08-03 15:40:52,254 - bmad.example - INFO - [complete_example.py:362] -   performance: {'max_concurrent_agents': 5, 'default_timeout': 30, 'retry_attempts': 3}
2025-08-03 15:40:52,254 - bmad.example - INFO - [complete_example.py:362] -   markdownExploder: True
2025-08-03 15:40:52,254 - bmad.example - INFO - [complete_example.py:362] -   prd: {'prdFile': 'docs/prd.md', 'prdVersion': 'v4', 'prdSharded': True, 'prdShardedLocation': 'docs/prd', 'epicFilePattern': 'epic-{n}*.md'}
2025-08-03 15:40:52,255 - bmad.example - INFO - [complete_example.py:362] -   architecture: {'architectureFile': 'docs/architecture.md', 'architectureVersion': 'v4', 'architectureSharded': True, 'architectureShardedLocation': 'docs/architecture'}
2025-08-03 15:40:52,255 - bmad.example - INFO - [complete_example.py:362] -   customTechnicalDocuments: None
2025-08-03 15:40:52,255 - bmad.example - INFO - [complete_example.py:362] -   devLoadAlwaysFiles: ['docs/architecture/coding-standards.md', 'docs/architecture/tech-stack.md', 'docs/architecture/source-tree.md']
2025-08-03 15:40:52,255 - bmad.example - INFO - [complete_example.py:362] -   devDebugLog: .ai/debug-log.md
2025-08-03 15:40:52,255 - bmad.example - INFO - [complete_example.py:362] -   devStoryLocation: docs/stories
2025-08-03 15:40:52,255 - bmad.example - INFO - [complete_example.py:362] -   slashPrefix: BMad
2025-08-03 15:40:52,255 - bmad.example - INFO - [complete_example.py:362] -   project_path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:40:52,256 - bmad.example - INFO - [complete_example.py:362] -   log_level: INFO
2025-08-03 15:40:52,256 - bmad.example - INFO - [complete_example.py:362] -   enable_monitoring: True
2025-08-03 15:40:52,256 - bmad.example - INFO - [complete_example.py:362] -   max_concurrent_agents: 3
2025-08-03 15:40:52,256 - bmad.example - INFO - [complete_example.py:370] - 
System Status:
2025-08-03 15:40:52,256 - bmad.example - INFO - [complete_example.py:371] -   Project Path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:40:52,256 - bmad.example - INFO - [complete_example.py:372] -   Monitoring Enabled: True
2025-08-03 15:40:52,256 - bmad.example - INFO - [complete_example.py:373] -   Log Level: INFO
2025-08-03 15:40:52,256 - bmad.example - INFO - [complete_example.py:72] - 
=== Demonstrating Orchestrator Request Routing ===
2025-08-03 15:40:52,257 - bmad.example - INFO - [complete_example.py:91] - 
Request 1: Analyze the current codebase structure and identify technical debt
2025-08-03 15:40:52,257 - bmad.example - ERROR - [complete_example.py:115] - Error processing request 1: 4 validation errors for AgentRequest
id
  Field required [type=missing, input_value={'content': 'Analyze the ...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
task_type
  Field required [type=missing, input_value={'content': 'Analyze the ...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
parameters
  Field required [type=missing, input_value={'content': 'Analyze the ...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
requester
  Field required [type=missing, input_value={'content': 'Analyze the ...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-03 15:40:52,257 - bmad.example - INFO - [complete_example.py:91] - 
Request 2: Design a microservices architecture for this monolithic application
2025-08-03 15:40:52,257 - bmad.example - ERROR - [complete_example.py:115] - Error processing request 2: 4 validation errors for AgentRequest
id
  Field required [type=missing, input_value={'content': 'Design a mic...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
task_type
  Field required [type=missing, input_value={'content': 'Design a mic...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
parameters
  Field required [type=missing, input_value={'content': 'Design a mic...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
requester
  Field required [type=missing, input_value={'content': 'Design a mic...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-03 15:40:52,258 - bmad.example - INFO - [complete_example.py:91] - 
Request 3: Help me understand how to use the BMad agents system
2025-08-03 15:40:52,258 - bmad.example - ERROR - [complete_example.py:115] - Error processing request 3: 4 validation errors for AgentRequest
id
  Field required [type=missing, input_value={'content': 'Help me unde...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
task_type
  Field required [type=missing, input_value={'content': 'Help me unde...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
parameters
  Field required [type=missing, input_value={'content': 'Help me unde...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
requester
  Field required [type=missing, input_value={'content': 'Help me unde...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-03 15:40:52,258 - bmad.example - INFO - [complete_example.py:119] - 
=== Demonstrating Direct Agent Usage ===
2025-08-03 15:40:52,258 - bmad.example - INFO - [complete_example.py:122] - 
Using AnalystAgent directly:
2025-08-03 15:40:53,029 - bmad.example - ERROR - [complete_example.py:402] - Example execution failed: 4 validation errors for AgentRequest
id
  Field required [type=missing, input_value={'content': 'Analyze the ...sis_type': 'structure'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
task_type
  Field required [type=missing, input_value={'content': 'Analyze the ...sis_type': 'structure'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
parameters
  Field required [type=missing, input_value={'content': 'Analyze the ...sis_type': 'structure'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
requester
  Field required [type=missing, input_value={'content': 'Analyze the ...sis_type': 'structure'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-03 15:42:09,436 - bmad.example - INFO - [complete_example.py:383] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 15:42:09,436 - bmad.example - INFO - [complete_example.py:384] - ============================================================
2025-08-03 15:42:09,436 - bmad.example - INFO - [complete_example.py:55] - Initializing BMad Pydantic AI Agents system...
2025-08-03 15:42:11,601 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:42:11,601 - bmad.example - INFO - [complete_example.py:68] - System initialized successfully
2025-08-03 15:42:11,601 - bmad.example - INFO - [complete_example.py:363] - 
=== System Information ===
2025-08-03 15:42:11,602 - bmad.example - INFO - [complete_example.py:366] - Configuration:
2025-08-03 15:42:11,602 - bmad.example - INFO - [complete_example.py:368] -   default_model: gemini-2.0-flash-exp
2025-08-03 15:42:11,602 - bmad.example - INFO - [complete_example.py:368] -   api_keys: {'google_ai': 'AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA', 'openai': '***********************************************************************************************', 'anthropic': '************************************************************************************************************'}
2025-08-03 15:42:11,602 - bmad.example - INFO - [complete_example.py:368] -   logging: {'level': 'INFO', 'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'}
2025-08-03 15:42:11,602 - bmad.example - INFO - [complete_example.py:368] -   state_management: {'enabled': True, 'state_dir': '.bmad_state', 'cleanup_days': 30}
2025-08-03 15:42:11,602 - bmad.example - INFO - [complete_example.py:368] -   performance: {'max_concurrent_agents': 5, 'default_timeout': 30, 'retry_attempts': 3}
2025-08-03 15:42:11,602 - bmad.example - INFO - [complete_example.py:368] -   markdownExploder: True
2025-08-03 15:42:11,603 - bmad.example - INFO - [complete_example.py:368] -   prd: {'prdFile': 'docs/prd.md', 'prdVersion': 'v4', 'prdSharded': True, 'prdShardedLocation': 'docs/prd', 'epicFilePattern': 'epic-{n}*.md'}
2025-08-03 15:42:11,603 - bmad.example - INFO - [complete_example.py:368] -   architecture: {'architectureFile': 'docs/architecture.md', 'architectureVersion': 'v4', 'architectureSharded': True, 'architectureShardedLocation': 'docs/architecture'}
2025-08-03 15:42:11,603 - bmad.example - INFO - [complete_example.py:368] -   customTechnicalDocuments: None
2025-08-03 15:42:11,603 - bmad.example - INFO - [complete_example.py:368] -   devLoadAlwaysFiles: ['docs/architecture/coding-standards.md', 'docs/architecture/tech-stack.md', 'docs/architecture/source-tree.md']
2025-08-03 15:42:11,603 - bmad.example - INFO - [complete_example.py:368] -   devDebugLog: .ai/debug-log.md
2025-08-03 15:42:11,603 - bmad.example - INFO - [complete_example.py:368] -   devStoryLocation: docs/stories
2025-08-03 15:42:11,603 - bmad.example - INFO - [complete_example.py:368] -   slashPrefix: BMad
2025-08-03 15:42:11,603 - bmad.example - INFO - [complete_example.py:368] -   project_path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:42:11,603 - bmad.example - INFO - [complete_example.py:368] -   log_level: INFO
2025-08-03 15:42:11,603 - bmad.example - INFO - [complete_example.py:368] -   enable_monitoring: True
2025-08-03 15:42:11,605 - bmad.example - INFO - [complete_example.py:368] -   max_concurrent_agents: 3
2025-08-03 15:42:11,605 - bmad.example - INFO - [complete_example.py:376] - 
System Status:
2025-08-03 15:42:11,605 - bmad.example - INFO - [complete_example.py:377] -   Project Path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:42:11,605 - bmad.example - INFO - [complete_example.py:378] -   Monitoring Enabled: True
2025-08-03 15:42:11,605 - bmad.example - INFO - [complete_example.py:379] -   Log Level: INFO
2025-08-03 15:42:11,605 - bmad.example - INFO - [complete_example.py:72] - 
=== Demonstrating Orchestrator Request Routing ===
2025-08-03 15:42:11,605 - bmad.example - INFO - [complete_example.py:91] - 
Request 1: Analyze the current codebase structure and identify technical debt
2025-08-03 15:42:13,883 - bmad.example - ERROR - [complete_example.py:116] - Error processing request 1: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:42:13,884 - bmad.example - INFO - [complete_example.py:91] - 
Request 2: Design a microservices architecture for this monolithic application
2025-08-03 15:42:15,965 - bmad.example - ERROR - [complete_example.py:116] - Error processing request 2: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:42:15,968 - bmad.example - INFO - [complete_example.py:91] - 
Request 3: Help me understand how to use the BMad agents system
2025-08-03 15:42:18,370 - bmad.example - ERROR - [complete_example.py:116] - Error processing request 3: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:42:18,370 - bmad.example - INFO - [complete_example.py:120] - 
=== Demonstrating Direct Agent Usage ===
2025-08-03 15:42:18,371 - bmad.example - INFO - [complete_example.py:123] - 
Using AnalystAgent directly:
2025-08-03 15:42:20,008 - bmad.example - ERROR - [complete_example.py:145] - Error with AnalystAgent: 'AgentRequest' object has no attribute 'get'
2025-08-03 15:42:20,009 - bmad.example - INFO - [complete_example.py:148] - 
Using ArchitectAgent directly:
2025-08-03 15:42:21,615 - bmad.example - ERROR - [complete_example.py:171] - Error with ArchitectAgent: 'AgentRequest' object has no attribute 'get'
2025-08-03 15:42:21,620 - bmad.example - INFO - [complete_example.py:175] - 
=== Demonstrating Workflow Execution ===
2025-08-03 15:42:21,621 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:42:28,182 - bmad.example - INFO - [complete_example.py:191] - Starting brownfield fullstack workflow...
2025-08-03 15:42:28,182 - bmad.example - ERROR - [complete_example.py:213] - Workflow execution error: 'BrownfieldFullstackWorkflow' object has no attribute 'execute'
2025-08-03 15:42:28,188 - bmad.example - INFO - [complete_example.py:217] - 
=== Demonstrating Help System ===
2025-08-03 15:42:28,189 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: How do I use the BMad agents system?
2025-08-03 15:42:30,843 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:42:30,844 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: What agents are available?
2025-08-03 15:42:33,177 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:42:33,178 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: How do I configure the system?
2025-08-03 15:42:35,238 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:42:35,238 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: What workflows can I use?
2025-08-03 15:42:36,752 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:42:36,753 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: How do I monitor performance?
2025-08-03 15:42:39,100 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:42:39,101 - bmad.example - INFO - [complete_example.py:278] - 
=== Demonstrating Error Handling ===
2025-08-03 15:42:39,101 - bmad.example - INFO - [complete_example.py:281] - 
Testing error handling with invalid request:
2025-08-03 15:42:41,198 - bmad.example - INFO - [complete_example.py:298] - 
Testing timeout handling:
2025-08-03 15:42:43,502 - bmad.example - INFO - [complete_example.py:317] - 
=== Production Error Handling Demonstration ===
2025-08-03 15:42:44,522 - bmad.example - INFO - [complete_example.py:351] - Testing circuit breaker pattern:
2025-08-03 15:42:44,523 - bmad.example - INFO - [complete_example.py:356] -   Attempt 1: AgentExecutionError - Service unavailable
2025-08-03 15:42:44,523 - bmad.example - INFO - [complete_example.py:356] -   Attempt 2: AgentExecutionError - Service unavailable
2025-08-03 15:42:44,523 - bmad.example - INFO - [complete_example.py:356] -   Attempt 3: AgentExecutionError - Circuit breaker is open
2025-08-03 15:42:44,526 - bmad.example - INFO - [complete_example.py:246] - 
=== Demonstrating Performance Monitoring ===
2025-08-03 15:42:44,526 - bmad.example - INFO - [complete_example.py:250] - Performance Summary:
2025-08-03 15:42:44,526 - bmad.example - INFO - [complete_example.py:251] -   Total operations: 0
2025-08-03 15:42:44,526 - bmad.example - INFO - [complete_example.py:252] -   Success rate: 0.00%
2025-08-03 15:42:44,527 - bmad.example - INFO - [complete_example.py:253] -   Average duration: 0.00s
2025-08-03 15:42:44,527 - bmad.example - INFO - [complete_example.py:254] -   Average memory usage: 0.0MB
2025-08-03 15:42:44,527 - bmad.example - INFO - [complete_example.py:263] - 
No recent errors found
2025-08-03 15:42:44,527 - bmad.example - INFO - [complete_example.py:404] - 
============================================================
2025-08-03 15:42:44,527 - bmad.example - INFO - [complete_example.py:405] - Complete example finished successfully!
2025-08-03 15:43:08,411 - bmad.example - INFO - [complete_example.py:383] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 15:43:08,411 - bmad.example - INFO - [complete_example.py:384] - ============================================================
2025-08-03 15:43:08,411 - bmad.example - INFO - [complete_example.py:55] - Initializing BMad Pydantic AI Agents system...
2025-08-03 15:43:10,603 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:43:10,603 - bmad.example - INFO - [complete_example.py:68] - System initialized successfully
2025-08-03 15:43:10,603 - bmad.example - INFO - [complete_example.py:363] - 
=== System Information ===
2025-08-03 15:43:10,603 - bmad.example - INFO - [complete_example.py:366] - Configuration:
2025-08-03 15:43:10,603 - bmad.example - INFO - [complete_example.py:368] -   default_model: gemini-2.0-flash-exp
2025-08-03 15:43:10,604 - bmad.example - INFO - [complete_example.py:368] -   api_keys: {'google_ai': 'AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA', 'openai': '***********************************************************************************************', 'anthropic': '************************************************************************************************************'}
2025-08-03 15:43:10,604 - bmad.example - INFO - [complete_example.py:368] -   logging: {'level': 'INFO', 'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'}
2025-08-03 15:43:10,604 - bmad.example - INFO - [complete_example.py:368] -   state_management: {'enabled': True, 'state_dir': '.bmad_state', 'cleanup_days': 30}
2025-08-03 15:43:10,604 - bmad.example - INFO - [complete_example.py:368] -   performance: {'max_concurrent_agents': 5, 'default_timeout': 30, 'retry_attempts': 3}
2025-08-03 15:43:10,604 - bmad.example - INFO - [complete_example.py:368] -   markdownExploder: True
2025-08-03 15:43:10,604 - bmad.example - INFO - [complete_example.py:368] -   prd: {'prdFile': 'docs/prd.md', 'prdVersion': 'v4', 'prdSharded': True, 'prdShardedLocation': 'docs/prd', 'epicFilePattern': 'epic-{n}*.md'}
2025-08-03 15:43:10,605 - bmad.example - INFO - [complete_example.py:368] -   architecture: {'architectureFile': 'docs/architecture.md', 'architectureVersion': 'v4', 'architectureSharded': True, 'architectureShardedLocation': 'docs/architecture'}
2025-08-03 15:43:10,605 - bmad.example - INFO - [complete_example.py:368] -   customTechnicalDocuments: None
2025-08-03 15:43:10,605 - bmad.example - INFO - [complete_example.py:368] -   devLoadAlwaysFiles: ['docs/architecture/coding-standards.md', 'docs/architecture/tech-stack.md', 'docs/architecture/source-tree.md']
2025-08-03 15:43:10,605 - bmad.example - INFO - [complete_example.py:368] -   devDebugLog: .ai/debug-log.md
2025-08-03 15:43:10,605 - bmad.example - INFO - [complete_example.py:368] -   devStoryLocation: docs/stories
2025-08-03 15:43:10,605 - bmad.example - INFO - [complete_example.py:368] -   slashPrefix: BMad
2025-08-03 15:43:10,605 - bmad.example - INFO - [complete_example.py:368] -   project_path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:43:10,605 - bmad.example - INFO - [complete_example.py:368] -   log_level: INFO
2025-08-03 15:43:10,606 - bmad.example - INFO - [complete_example.py:368] -   enable_monitoring: True
2025-08-03 15:43:10,606 - bmad.example - INFO - [complete_example.py:368] -   max_concurrent_agents: 3
2025-08-03 15:43:10,606 - bmad.example - INFO - [complete_example.py:376] - 
System Status:
2025-08-03 15:43:10,606 - bmad.example - INFO - [complete_example.py:377] -   Project Path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:43:10,606 - bmad.example - INFO - [complete_example.py:378] -   Monitoring Enabled: True
2025-08-03 15:43:10,606 - bmad.example - INFO - [complete_example.py:379] -   Log Level: INFO
2025-08-03 15:43:10,606 - bmad.example - INFO - [complete_example.py:72] - 
=== Demonstrating Orchestrator Request Routing ===
2025-08-03 15:43:10,606 - bmad.example - INFO - [complete_example.py:91] - 
Request 1: Analyze the current codebase structure and identify technical debt
2025-08-03 15:43:12,704 - bmad.example - ERROR - [complete_example.py:116] - Error processing request 1: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:43:12,705 - bmad.example - INFO - [complete_example.py:91] - 
Request 2: Design a microservices architecture for this monolithic application
2025-08-03 15:43:14,857 - bmad.example - ERROR - [complete_example.py:116] - Error processing request 2: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:43:14,858 - bmad.example - INFO - [complete_example.py:91] - 
Request 3: Help me understand how to use the BMad agents system
2025-08-03 15:43:17,317 - bmad.example - ERROR - [complete_example.py:116] - Error processing request 3: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:43:17,318 - bmad.example - INFO - [complete_example.py:120] - 
=== Demonstrating Direct Agent Usage ===
2025-08-03 15:43:17,318 - bmad.example - INFO - [complete_example.py:123] - 
Using AnalystAgent directly:
2025-08-03 15:43:18,076 - bmad.example - ERROR - [complete_example.py:145] - Error with AnalystAgent: 'AgentRequest' object has no attribute 'get'
2025-08-03 15:43:18,077 - bmad.example - INFO - [complete_example.py:148] - 
Using ArchitectAgent directly:
2025-08-03 15:43:18,825 - bmad.example - ERROR - [complete_example.py:171] - Error with ArchitectAgent: 'AgentRequest' object has no attribute 'get'
2025-08-03 15:43:18,827 - bmad.example - INFO - [complete_example.py:175] - 
=== Demonstrating Workflow Execution ===
2025-08-03 15:43:18,828 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:43:23,271 - bmad.example - INFO - [complete_example.py:191] - Starting brownfield fullstack workflow...
2025-08-03 15:43:23,271 - bmad.example - ERROR - [complete_example.py:213] - Workflow execution error: 'BrownfieldFullstackWorkflow' object has no attribute 'execute'
2025-08-03 15:43:23,276 - bmad.example - INFO - [complete_example.py:217] - 
=== Demonstrating Help System ===
2025-08-03 15:43:23,277 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: How do I use the BMad agents system?
2025-08-03 15:43:25,530 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:43:25,532 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: What agents are available?
2025-08-03 15:43:27,709 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:43:27,710 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: How do I configure the system?
2025-08-03 15:43:29,884 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:43:29,885 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: What workflows can I use?
2025-08-03 15:43:32,185 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:43:32,186 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: How do I monitor performance?
2025-08-03 15:43:33,673 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:43:33,673 - bmad.example - INFO - [complete_example.py:278] - 
=== Demonstrating Error Handling ===
2025-08-03 15:43:33,673 - bmad.example - INFO - [complete_example.py:281] - 
Testing error handling with invalid request:
2025-08-03 15:43:35,737 - bmad.example - INFO - [complete_example.py:295] - [OK] Error handled correctly: AttributeError: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:43:35,737 - bmad.example - INFO - [complete_example.py:298] - 
Testing timeout handling:
2025-08-03 15:43:37,771 - bmad.example - INFO - [complete_example.py:317] - 
=== Production Error Handling Demonstration ===
2025-08-03 15:43:37,771 - bmad - WARNING - [error_handling.py:39] - Attempt 1 failed, retrying in 0.5s: Simulated agent failure
2025-08-03 15:43:38,272 - bmad - WARNING - [error_handling.py:39] - Attempt 2 failed, retrying in 1.0s: Simulated agent failure
2025-08-03 15:43:39,282 - bmad.example - INFO - [complete_example.py:329] - [OK] Retry operation result: Operation succeeded after retries
2025-08-03 15:43:40,290 - bmad.example - INFO - [complete_example.py:343] - [OK] Timeout handled correctly: Agent execution timed out after 1s
2025-08-03 15:43:40,291 - bmad.example - INFO - [complete_example.py:351] - Testing circuit breaker pattern:
2025-08-03 15:43:40,291 - bmad.example - INFO - [complete_example.py:356] -   Attempt 1: AgentExecutionError - Service unavailable
2025-08-03 15:43:40,291 - bmad.example - INFO - [complete_example.py:356] -   Attempt 2: AgentExecutionError - Service unavailable
2025-08-03 15:43:40,291 - bmad.example - INFO - [complete_example.py:356] -   Attempt 3: AgentExecutionError - Circuit breaker is open
2025-08-03 15:43:40,292 - bmad.example - INFO - [complete_example.py:358] -   [OK] Circuit breaker opened successfully
2025-08-03 15:43:40,292 - bmad.example - INFO - [complete_example.py:246] - 
=== Demonstrating Performance Monitoring ===
2025-08-03 15:43:40,292 - bmad.example - INFO - [complete_example.py:250] - Performance Summary:
2025-08-03 15:43:40,292 - bmad.example - INFO - [complete_example.py:251] -   Total operations: 0
2025-08-03 15:43:40,292 - bmad.example - INFO - [complete_example.py:252] -   Success rate: 0.00%
2025-08-03 15:43:40,292 - bmad.example - INFO - [complete_example.py:253] -   Average duration: 0.00s
2025-08-03 15:43:40,293 - bmad.example - INFO - [complete_example.py:254] -   Average memory usage: 0.0MB
2025-08-03 15:43:40,293 - bmad.example - INFO - [complete_example.py:263] - 
No recent errors found
2025-08-03 15:43:40,293 - bmad.example - INFO - [complete_example.py:404] - 
============================================================
2025-08-03 15:43:40,293 - bmad.example - INFO - [complete_example.py:405] - Complete example finished successfully!
2025-08-03 15:43:56,957 - bmad.example - INFO - [complete_example.py:383] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 15:43:56,957 - bmad.example - INFO - [complete_example.py:384] - ============================================================
2025-08-03 15:43:56,958 - bmad.example - INFO - [complete_example.py:55] - Initializing BMad Pydantic AI Agents system...
2025-08-03 15:43:59,188 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:43:59,188 - bmad.example - INFO - [complete_example.py:68] - System initialized successfully
2025-08-03 15:43:59,189 - bmad.example - INFO - [complete_example.py:363] - 
=== System Information ===
2025-08-03 15:43:59,189 - bmad.example - INFO - [complete_example.py:366] - Configuration:
2025-08-03 15:43:59,189 - bmad.example - INFO - [complete_example.py:368] -   default_model: gemini-2.0-flash-exp
2025-08-03 15:43:59,190 - bmad.example - INFO - [complete_example.py:368] -   api_keys: {'google_ai': 'AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA', 'openai': '***********************************************************************************************', 'anthropic': '************************************************************************************************************'}
2025-08-03 15:43:59,190 - bmad.example - INFO - [complete_example.py:368] -   logging: {'level': 'INFO', 'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'}
2025-08-03 15:43:59,190 - bmad.example - INFO - [complete_example.py:368] -   state_management: {'enabled': True, 'state_dir': '.bmad_state', 'cleanup_days': 30}
2025-08-03 15:43:59,190 - bmad.example - INFO - [complete_example.py:368] -   performance: {'max_concurrent_agents': 5, 'default_timeout': 30, 'retry_attempts': 3}
2025-08-03 15:43:59,190 - bmad.example - INFO - [complete_example.py:368] -   markdownExploder: True
2025-08-03 15:43:59,190 - bmad.example - INFO - [complete_example.py:368] -   prd: {'prdFile': 'docs/prd.md', 'prdVersion': 'v4', 'prdSharded': True, 'prdShardedLocation': 'docs/prd', 'epicFilePattern': 'epic-{n}*.md'}
2025-08-03 15:43:59,191 - bmad.example - INFO - [complete_example.py:368] -   architecture: {'architectureFile': 'docs/architecture.md', 'architectureVersion': 'v4', 'architectureSharded': True, 'architectureShardedLocation': 'docs/architecture'}
2025-08-03 15:43:59,191 - bmad.example - INFO - [complete_example.py:368] -   customTechnicalDocuments: None
2025-08-03 15:43:59,191 - bmad.example - INFO - [complete_example.py:368] -   devLoadAlwaysFiles: ['docs/architecture/coding-standards.md', 'docs/architecture/tech-stack.md', 'docs/architecture/source-tree.md']
2025-08-03 15:43:59,191 - bmad.example - INFO - [complete_example.py:368] -   devDebugLog: .ai/debug-log.md
2025-08-03 15:43:59,191 - bmad.example - INFO - [complete_example.py:368] -   devStoryLocation: docs/stories
2025-08-03 15:43:59,191 - bmad.example - INFO - [complete_example.py:368] -   slashPrefix: BMad
2025-08-03 15:43:59,191 - bmad.example - INFO - [complete_example.py:368] -   project_path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:43:59,191 - bmad.example - INFO - [complete_example.py:368] -   log_level: INFO
2025-08-03 15:43:59,192 - bmad.example - INFO - [complete_example.py:368] -   enable_monitoring: True
2025-08-03 15:43:59,192 - bmad.example - INFO - [complete_example.py:368] -   max_concurrent_agents: 3
2025-08-03 15:43:59,192 - bmad.example - INFO - [complete_example.py:376] - 
System Status:
2025-08-03 15:43:59,192 - bmad.example - INFO - [complete_example.py:377] -   Project Path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:43:59,192 - bmad.example - INFO - [complete_example.py:378] -   Monitoring Enabled: True
2025-08-03 15:43:59,192 - bmad.example - INFO - [complete_example.py:379] -   Log Level: INFO
2025-08-03 15:43:59,192 - bmad.example - INFO - [complete_example.py:72] - 
=== Demonstrating Orchestrator Request Routing ===
2025-08-03 15:43:59,193 - bmad.example - INFO - [complete_example.py:91] - 
Request 1: Analyze the current codebase structure and identify technical debt
2025-08-03 15:44:01,298 - bmad.example - ERROR - [complete_example.py:116] - Error processing request 1: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:44:01,299 - bmad.example - INFO - [complete_example.py:91] - 
Request 2: Design a microservices architecture for this monolithic application
2025-08-03 15:44:03,484 - bmad.example - ERROR - [complete_example.py:116] - Error processing request 2: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:44:03,485 - bmad.example - INFO - [complete_example.py:91] - 
Request 3: Help me understand how to use the BMad agents system
2025-08-03 15:44:04,797 - bmad.example - ERROR - [complete_example.py:116] - Error processing request 3: 429 RESOURCE_EXHAUSTED. {'error': {'code': 429, 'message': 'You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.', 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.QuotaFailure', 'violations': [{'quotaMetric': 'generativelanguage.googleapis.com/generate_requests_per_model', 'quotaId': 'GenerateRequestsPerMinutePerProjectPerModel', 'quotaDimensions': {'location': 'global', 'model': 'gemini-2.0-flash-exp'}, 'quotaValue': '10'}]}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Learn more about Gemini API quotas', 'url': 'https://ai.google.dev/gemini-api/docs/rate-limits'}]}, {'@type': 'type.googleapis.com/google.rpc.RetryInfo', 'retryDelay': '0s'}]}}
2025-08-03 15:44:04,797 - bmad.example - INFO - [complete_example.py:120] - 
=== Demonstrating Direct Agent Usage ===
2025-08-03 15:44:04,798 - bmad.example - INFO - [complete_example.py:123] - 
Using AnalystAgent directly:
2025-08-03 15:44:05,569 - bmad.example - ERROR - [complete_example.py:145] - Error with AnalystAgent: 'AgentRequest' object has no attribute 'get'
2025-08-03 15:44:05,569 - bmad.example - INFO - [complete_example.py:148] - 
Using ArchitectAgent directly:
2025-08-03 15:44:06,342 - bmad.example - ERROR - [complete_example.py:171] - Error with ArchitectAgent: 'AgentRequest' object has no attribute 'get'
2025-08-03 15:44:06,344 - bmad.example - INFO - [complete_example.py:175] - 
=== Demonstrating Workflow Execution ===
2025-08-03 15:44:06,345 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:44:10,901 - bmad.example - INFO - [complete_example.py:191] - Starting brownfield fullstack workflow...
2025-08-03 15:44:10,901 - bmad.example - ERROR - [complete_example.py:213] - Workflow execution error: 'BrownfieldFullstackWorkflow' object has no attribute 'execute'
2025-08-03 15:44:10,908 - bmad.example - INFO - [complete_example.py:217] - 
=== Demonstrating Help System ===
2025-08-03 15:44:10,908 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: How do I use the BMad agents system?
2025-08-03 15:44:12,158 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 429 RESOURCE_EXHAUSTED. {'error': {'code': 429, 'message': 'You exceeded your current quota. Please migrate to Gemini 2.0 Flash Preview (Image Generation) (models/gemini-2.0-flash-preview-image-generation) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.', 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.QuotaFailure', 'violations': [{'quotaMetric': 'generativelanguage.googleapis.com/generate_requests_per_model', 'quotaId': 'GenerateRequestsPerMinutePerProjectPerModel', 'quotaDimensions': {'location': 'global', 'model': 'gemini-2.0-flash-exp'}, 'quotaValue': '10'}]}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Learn more about Gemini API quotas', 'url': 'https://ai.google.dev/gemini-api/docs/rate-limits'}]}, {'@type': 'type.googleapis.com/google.rpc.RetryInfo', 'retryDelay': '53s'}]}}
2025-08-03 15:44:12,160 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: What agents are available?
2025-08-03 15:44:15,308 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:44:15,308 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: How do I configure the system?
2025-08-03 15:44:17,598 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:44:17,599 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: What workflows can I use?
2025-08-03 15:44:19,811 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:44:19,812 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: How do I monitor performance?
2025-08-03 15:44:22,062 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:44:22,063 - bmad.example - INFO - [complete_example.py:278] - 
=== Demonstrating Error Handling ===
2025-08-03 15:44:22,063 - bmad.example - INFO - [complete_example.py:281] - 
Testing error handling with invalid request:
2025-08-03 15:46:51,902 - bmad.example - INFO - [complete_example.py:383] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 15:46:51,903 - bmad.example - INFO - [complete_example.py:384] - ============================================================
2025-08-03 15:46:51,903 - bmad.example - INFO - [complete_example.py:55] - Initializing BMad Pydantic AI Agents system...
2025-08-03 15:46:54,136 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:46:54,136 - bmad.example - INFO - [complete_example.py:68] - System initialized successfully
2025-08-03 15:46:54,136 - bmad.example - INFO - [complete_example.py:363] - 
=== System Information ===
2025-08-03 15:46:54,137 - bmad.example - INFO - [complete_example.py:366] - Configuration:
2025-08-03 15:46:54,137 - bmad.example - INFO - [complete_example.py:368] -   default_model: gemini-2.5-flash
2025-08-03 15:46:54,137 - bmad.example - INFO - [complete_example.py:368] -   api_keys: {'google_ai': 'AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA', 'openai': '***********************************************************************************************', 'anthropic': '************************************************************************************************************'}
2025-08-03 15:46:54,137 - bmad.example - INFO - [complete_example.py:368] -   logging: {'level': 'INFO', 'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'}
2025-08-03 15:46:54,137 - bmad.example - INFO - [complete_example.py:368] -   state_management: {'enabled': True, 'state_dir': '.bmad_state', 'cleanup_days': 30}
2025-08-03 15:46:54,137 - bmad.example - INFO - [complete_example.py:368] -   performance: {'max_concurrent_agents': 5, 'default_timeout': 30, 'retry_attempts': 3}
2025-08-03 15:46:54,138 - bmad.example - INFO - [complete_example.py:368] -   markdownExploder: True
2025-08-03 15:46:54,138 - bmad.example - INFO - [complete_example.py:368] -   prd: {'prdFile': 'docs/prd.md', 'prdVersion': 'v4', 'prdSharded': True, 'prdShardedLocation': 'docs/prd', 'epicFilePattern': 'epic-{n}*.md'}
2025-08-03 15:46:54,138 - bmad.example - INFO - [complete_example.py:368] -   architecture: {'architectureFile': 'docs/architecture.md', 'architectureVersion': 'v4', 'architectureSharded': True, 'architectureShardedLocation': 'docs/architecture'}
2025-08-03 15:46:54,138 - bmad.example - INFO - [complete_example.py:368] -   customTechnicalDocuments: None
2025-08-03 15:46:54,138 - bmad.example - INFO - [complete_example.py:368] -   devLoadAlwaysFiles: ['docs/architecture/coding-standards.md', 'docs/architecture/tech-stack.md', 'docs/architecture/source-tree.md']
2025-08-03 15:46:54,138 - bmad.example - INFO - [complete_example.py:368] -   devDebugLog: .ai/debug-log.md
2025-08-03 15:46:54,138 - bmad.example - INFO - [complete_example.py:368] -   devStoryLocation: docs/stories
2025-08-03 15:46:54,139 - bmad.example - INFO - [complete_example.py:368] -   slashPrefix: BMad
2025-08-03 15:46:54,139 - bmad.example - INFO - [complete_example.py:368] -   project_path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:46:54,139 - bmad.example - INFO - [complete_example.py:368] -   log_level: INFO
2025-08-03 15:46:54,139 - bmad.example - INFO - [complete_example.py:368] -   enable_monitoring: True
2025-08-03 15:46:54,139 - bmad.example - INFO - [complete_example.py:368] -   max_concurrent_agents: 3
2025-08-03 15:46:54,139 - bmad.example - INFO - [complete_example.py:376] - 
System Status:
2025-08-03 15:46:54,139 - bmad.example - INFO - [complete_example.py:377] -   Project Path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:46:54,139 - bmad.example - INFO - [complete_example.py:378] -   Monitoring Enabled: True
2025-08-03 15:46:54,139 - bmad.example - INFO - [complete_example.py:379] -   Log Level: INFO
2025-08-03 15:46:54,139 - bmad.example - INFO - [complete_example.py:72] - 
=== Demonstrating Orchestrator Request Routing ===
2025-08-03 15:46:54,140 - bmad.example - INFO - [complete_example.py:91] - 
Request 1: Analyze the current codebase structure and identify technical debt
2025-08-03 15:46:56,891 - bmad.example - ERROR - [complete_example.py:116] - Error processing request 1: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:46:56,892 - bmad.example - INFO - [complete_example.py:91] - 
Request 2: Design a microservices architecture for this monolithic application
2025-08-03 15:46:58,537 - bmad.example - ERROR - [complete_example.py:116] - Error processing request 2: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:46:58,537 - bmad.example - INFO - [complete_example.py:91] - 
Request 3: Help me understand how to use the BMad agents system
2025-08-03 15:47:02,762 - bmad.example - ERROR - [complete_example.py:116] - Error processing request 3: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:47:02,762 - bmad.example - INFO - [complete_example.py:120] - 
=== Demonstrating Direct Agent Usage ===
2025-08-03 15:47:02,762 - bmad.example - INFO - [complete_example.py:123] - 
Using AnalystAgent directly:
2025-08-03 15:47:03,527 - bmad.example - ERROR - [complete_example.py:145] - Error with AnalystAgent: 'AgentRequest' object has no attribute 'get'
2025-08-03 15:47:03,527 - bmad.example - INFO - [complete_example.py:148] - 
Using ArchitectAgent directly:
2025-08-03 15:47:04,283 - bmad.example - ERROR - [complete_example.py:171] - Error with ArchitectAgent: 'AgentRequest' object has no attribute 'get'
2025-08-03 15:47:04,285 - bmad.example - INFO - [complete_example.py:175] - 
=== Demonstrating Workflow Execution ===
2025-08-03 15:47:04,286 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:47:08,901 - bmad.example - INFO - [complete_example.py:191] - Starting brownfield fullstack workflow...
2025-08-03 15:47:08,901 - bmad.example - ERROR - [complete_example.py:213] - Workflow execution error: 'BrownfieldFullstackWorkflow' object has no attribute 'execute'
2025-08-03 15:47:08,906 - bmad.example - INFO - [complete_example.py:217] - 
=== Demonstrating Help System ===
2025-08-03 15:47:08,907 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: How do I use the BMad agents system?
2025-08-03 15:47:11,462 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:47:11,462 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: What agents are available?
2025-08-03 15:47:14,315 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:47:14,315 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: How do I configure the system?
2025-08-03 15:47:20,206 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:47:20,207 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: What workflows can I use?
2025-08-03 15:47:22,514 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:47:22,515 - bmad.example - INFO - [complete_example.py:228] - 
Help Query: How do I monitor performance?
2025-08-03 15:47:24,027 - bmad.example - ERROR - [complete_example.py:242] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:47:24,028 - bmad.example - INFO - [complete_example.py:278] - 
=== Demonstrating Error Handling ===
2025-08-03 15:47:24,028 - bmad.example - INFO - [complete_example.py:281] - 
Testing error handling with invalid request:
2025-08-03 15:47:27,819 - bmad.example - INFO - [complete_example.py:295] - [OK] Error handled correctly: AttributeError: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:47:27,819 - bmad.example - INFO - [complete_example.py:298] - 
Testing timeout handling:
2025-08-03 15:47:30,860 - bmad.example - INFO - [complete_example.py:313] - [OK] Timeout handled: AttributeError: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:47:30,860 - bmad.example - INFO - [complete_example.py:317] - 
=== Production Error Handling Demonstration ===
2025-08-03 15:47:30,860 - bmad - WARNING - [error_handling.py:39] - Attempt 1 failed, retrying in 0.5s: Simulated agent failure
2025-08-03 15:47:31,371 - bmad.example - INFO - [complete_example.py:329] - [OK] Retry operation result: Operation succeeded after retries
2025-08-03 15:47:32,383 - bmad.example - INFO - [complete_example.py:343] - [OK] Timeout handled correctly: Agent execution timed out after 1s
2025-08-03 15:47:32,383 - bmad.example - INFO - [complete_example.py:351] - Testing circuit breaker pattern:
2025-08-03 15:47:32,383 - bmad.example - INFO - [complete_example.py:356] -   Attempt 1: AgentExecutionError - Service unavailable
2025-08-03 15:47:32,384 - bmad.example - INFO - [complete_example.py:356] -   Attempt 2: AgentExecutionError - Service unavailable
2025-08-03 15:47:32,384 - bmad.example - INFO - [complete_example.py:356] -   Attempt 3: AgentExecutionError - Circuit breaker is open
2025-08-03 15:47:32,385 - bmad.example - INFO - [complete_example.py:358] -   [OK] Circuit breaker opened successfully
2025-08-03 15:47:32,385 - bmad.example - INFO - [complete_example.py:246] - 
=== Demonstrating Performance Monitoring ===
2025-08-03 15:47:32,385 - bmad.example - INFO - [complete_example.py:250] - Performance Summary:
2025-08-03 15:47:32,385 - bmad.example - INFO - [complete_example.py:251] -   Total operations: 0
2025-08-03 15:47:32,386 - bmad.example - INFO - [complete_example.py:252] -   Success rate: 0.00%
2025-08-03 15:47:32,386 - bmad.example - INFO - [complete_example.py:253] -   Average duration: 0.00s
2025-08-03 15:47:32,386 - bmad.example - INFO - [complete_example.py:254] -   Average memory usage: 0.0MB
2025-08-03 15:47:32,386 - bmad.example - INFO - [complete_example.py:263] - 
No recent errors found
2025-08-03 15:47:32,386 - bmad.example - INFO - [complete_example.py:404] - 
============================================================
2025-08-03 15:47:32,387 - bmad.example - INFO - [complete_example.py:405] - Complete example finished successfully!
2025-08-03 15:50:46,825 - bmad.example - INFO - [complete_example.py:388] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 15:50:46,825 - bmad.example - INFO - [complete_example.py:389] - ============================================================
2025-08-03 15:50:46,825 - bmad.example - INFO - [complete_example.py:60] - Initializing BMad Pydantic AI Agents system...
2025-08-03 15:50:49,036 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:50:49,036 - bmad.example - INFO - [complete_example.py:73] - System initialized successfully
2025-08-03 15:50:49,036 - bmad.example - INFO - [complete_example.py:368] - 
=== System Information ===
2025-08-03 15:50:49,036 - bmad.example - INFO - [complete_example.py:371] - Configuration:
2025-08-03 15:50:49,036 - bmad.example - INFO - [complete_example.py:373] -   default_model: gemini-2.5-flash
2025-08-03 15:50:49,037 - bmad.example - INFO - [complete_example.py:373] -   api_keys: {'google_ai': 'AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA', 'openai': '***********************************************************************************************', 'anthropic': '************************************************************************************************************'}
2025-08-03 15:50:49,037 - bmad.example - INFO - [complete_example.py:373] -   logging: {'level': 'INFO', 'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'}
2025-08-03 15:50:49,037 - bmad.example - INFO - [complete_example.py:373] -   state_management: {'enabled': True, 'state_dir': '.bmad_state', 'cleanup_days': 30}
2025-08-03 15:50:49,037 - bmad.example - INFO - [complete_example.py:373] -   performance: {'max_concurrent_agents': 5, 'default_timeout': 30, 'retry_attempts': 3}
2025-08-03 15:50:49,037 - bmad.example - INFO - [complete_example.py:373] -   markdownExploder: True
2025-08-03 15:50:49,037 - bmad.example - INFO - [complete_example.py:373] -   prd: {'prdFile': 'docs/prd.md', 'prdVersion': 'v4', 'prdSharded': True, 'prdShardedLocation': 'docs/prd', 'epicFilePattern': 'epic-{n}*.md'}
2025-08-03 15:50:49,037 - bmad.example - INFO - [complete_example.py:373] -   architecture: {'architectureFile': 'docs/architecture.md', 'architectureVersion': 'v4', 'architectureSharded': True, 'architectureShardedLocation': 'docs/architecture'}
2025-08-03 15:50:49,037 - bmad.example - INFO - [complete_example.py:373] -   customTechnicalDocuments: None
2025-08-03 15:50:49,038 - bmad.example - INFO - [complete_example.py:373] -   devLoadAlwaysFiles: ['docs/architecture/coding-standards.md', 'docs/architecture/tech-stack.md', 'docs/architecture/source-tree.md']
2025-08-03 15:50:49,038 - bmad.example - INFO - [complete_example.py:373] -   devDebugLog: .ai/debug-log.md
2025-08-03 15:50:49,038 - bmad.example - INFO - [complete_example.py:373] -   devStoryLocation: docs/stories
2025-08-03 15:50:49,038 - bmad.example - INFO - [complete_example.py:373] -   slashPrefix: BMad
2025-08-03 15:50:49,038 - bmad.example - INFO - [complete_example.py:373] -   project_path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:50:49,038 - bmad.example - INFO - [complete_example.py:373] -   log_level: INFO
2025-08-03 15:50:49,038 - bmad.example - INFO - [complete_example.py:373] -   enable_monitoring: True
2025-08-03 15:50:49,038 - bmad.example - INFO - [complete_example.py:373] -   max_concurrent_agents: 3
2025-08-03 15:50:49,039 - bmad.example - INFO - [complete_example.py:381] - 
System Status:
2025-08-03 15:50:49,039 - bmad.example - INFO - [complete_example.py:382] -   Project Path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:50:49,039 - bmad.example - INFO - [complete_example.py:383] -   Monitoring Enabled: True
2025-08-03 15:50:49,039 - bmad.example - INFO - [complete_example.py:384] -   Log Level: INFO
2025-08-03 15:50:49,039 - bmad.example - INFO - [complete_example.py:77] - 
=== Demonstrating Orchestrator Request Routing ===
2025-08-03 15:50:49,039 - bmad.example - INFO - [complete_example.py:96] - 
Request 1: Analyze the current codebase structure and identify technical debt
2025-08-03 15:50:49,040 - bmad.example - ERROR - [complete_example.py:121] - Error processing request 1: 3 validation errors for AgentRequest
request_id
  Field required [type=missing, input_value={'id': 'orchestrator_req_...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
agent_role
  Field required [type=missing, input_value={'id': 'orchestrator_req_...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
action
  Field required [type=missing, input_value={'id': 'orchestrator_req_...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-03 15:50:49,040 - bmad.example - INFO - [complete_example.py:96] - 
Request 2: Design a microservices architecture for this monolithic application
2025-08-03 15:50:49,040 - bmad.example - ERROR - [complete_example.py:121] - Error processing request 2: 3 validation errors for AgentRequest
request_id
  Field required [type=missing, input_value={'id': 'orchestrator_req_...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
agent_role
  Field required [type=missing, input_value={'id': 'orchestrator_req_...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
action
  Field required [type=missing, input_value={'id': 'orchestrator_req_...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-03 15:50:49,040 - bmad.example - INFO - [complete_example.py:96] - 
Request 3: Help me understand how to use the BMad agents system
2025-08-03 15:50:49,041 - bmad.example - ERROR - [complete_example.py:121] - Error processing request 3: 3 validation errors for AgentRequest
request_id
  Field required [type=missing, input_value={'id': 'orchestrator_req_...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
agent_role
  Field required [type=missing, input_value={'id': 'orchestrator_req_...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
action
  Field required [type=missing, input_value={'id': 'orchestrator_req_...s\\BMADPydanticAgents'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-03 15:50:49,041 - bmad.example - INFO - [complete_example.py:125] - 
=== Demonstrating Direct Agent Usage ===
2025-08-03 15:50:49,041 - bmad.example - INFO - [complete_example.py:128] - 
Using AnalystAgent directly:
2025-08-03 15:50:49,795 - bmad.example - ERROR - [complete_example.py:413] - Example execution failed: 3 validation errors for AgentRequest
request_id
  Field required [type=missing, input_value={'id': 'analyst_req_1', '...sis_type': 'structure'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
agent_role
  Field required [type=missing, input_value={'id': 'analyst_req_1', '...sis_type': 'structure'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
action
  Field required [type=missing, input_value={'id': 'analyst_req_1', '...sis_type': 'structure'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-03 15:51:51,326 - bmad.example - INFO - [complete_example.py:392] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 15:51:51,326 - bmad.example - INFO - [complete_example.py:393] - ============================================================
2025-08-03 15:51:51,326 - bmad.example - INFO - [complete_example.py:60] - Initializing BMad Pydantic AI Agents system...
2025-08-03 15:51:53,532 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:51:53,532 - bmad.example - INFO - [complete_example.py:73] - System initialized successfully
2025-08-03 15:51:53,532 - bmad.example - INFO - [complete_example.py:372] - 
=== System Information ===
2025-08-03 15:51:53,532 - bmad.example - INFO - [complete_example.py:375] - Configuration:
2025-08-03 15:51:53,532 - bmad.example - INFO - [complete_example.py:377] -   default_model: gemini-2.5-flash
2025-08-03 15:51:53,533 - bmad.example - INFO - [complete_example.py:377] -   api_keys: {'google_ai': 'AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA', 'openai': '***********************************************************************************************', 'anthropic': '************************************************************************************************************'}
2025-08-03 15:51:53,533 - bmad.example - INFO - [complete_example.py:377] -   logging: {'level': 'INFO', 'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'}
2025-08-03 15:51:53,533 - bmad.example - INFO - [complete_example.py:377] -   state_management: {'enabled': True, 'state_dir': '.bmad_state', 'cleanup_days': 30}
2025-08-03 15:51:53,533 - bmad.example - INFO - [complete_example.py:377] -   performance: {'max_concurrent_agents': 5, 'default_timeout': 30, 'retry_attempts': 3}
2025-08-03 15:51:53,533 - bmad.example - INFO - [complete_example.py:377] -   markdownExploder: True
2025-08-03 15:51:53,533 - bmad.example - INFO - [complete_example.py:377] -   prd: {'prdFile': 'docs/prd.md', 'prdVersion': 'v4', 'prdSharded': True, 'prdShardedLocation': 'docs/prd', 'epicFilePattern': 'epic-{n}*.md'}
2025-08-03 15:51:53,533 - bmad.example - INFO - [complete_example.py:377] -   architecture: {'architectureFile': 'docs/architecture.md', 'architectureVersion': 'v4', 'architectureSharded': True, 'architectureShardedLocation': 'docs/architecture'}
2025-08-03 15:51:53,534 - bmad.example - INFO - [complete_example.py:377] -   customTechnicalDocuments: None
2025-08-03 15:51:53,534 - bmad.example - INFO - [complete_example.py:377] -   devLoadAlwaysFiles: ['docs/architecture/coding-standards.md', 'docs/architecture/tech-stack.md', 'docs/architecture/source-tree.md']
2025-08-03 15:51:53,534 - bmad.example - INFO - [complete_example.py:377] -   devDebugLog: .ai/debug-log.md
2025-08-03 15:51:53,534 - bmad.example - INFO - [complete_example.py:377] -   devStoryLocation: docs/stories
2025-08-03 15:51:53,534 - bmad.example - INFO - [complete_example.py:377] -   slashPrefix: BMad
2025-08-03 15:51:53,534 - bmad.example - INFO - [complete_example.py:377] -   project_path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:51:53,534 - bmad.example - INFO - [complete_example.py:377] -   log_level: INFO
2025-08-03 15:51:53,534 - bmad.example - INFO - [complete_example.py:377] -   enable_monitoring: True
2025-08-03 15:51:53,535 - bmad.example - INFO - [complete_example.py:377] -   max_concurrent_agents: 3
2025-08-03 15:51:53,535 - bmad.example - INFO - [complete_example.py:385] - 
System Status:
2025-08-03 15:51:53,535 - bmad.example - INFO - [complete_example.py:386] -   Project Path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:51:53,535 - bmad.example - INFO - [complete_example.py:387] -   Monitoring Enabled: True
2025-08-03 15:51:53,535 - bmad.example - INFO - [complete_example.py:388] -   Log Level: INFO
2025-08-03 15:51:53,535 - bmad.example - INFO - [complete_example.py:77] - 
=== Demonstrating Orchestrator Request Routing ===
2025-08-03 15:51:53,535 - bmad.example - INFO - [complete_example.py:96] - 
Request 1: Analyze the current codebase structure and identify technical debt
2025-08-03 15:51:56,342 - bmad.example - ERROR - [complete_example.py:121] - Error processing request 1: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:51:56,343 - bmad.example - INFO - [complete_example.py:96] - 
Request 2: Design a microservices architecture for this monolithic application
2025-08-03 15:51:58,761 - bmad.example - ERROR - [complete_example.py:121] - Error processing request 2: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:51:58,762 - bmad.example - INFO - [complete_example.py:96] - 
Request 3: Help me understand how to use the BMad agents system
2025-08-03 15:52:03,055 - bmad.example - ERROR - [complete_example.py:121] - Error processing request 3: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:52:03,056 - bmad.example - INFO - [complete_example.py:125] - 
=== Demonstrating Direct Agent Usage ===
2025-08-03 15:52:03,056 - bmad.example - INFO - [complete_example.py:128] - 
Using AnalystAgent directly:
2025-08-03 15:52:04,710 - bmad.example - ERROR - [complete_example.py:151] - Error with AnalystAgent: 'AgentRequest' object has no attribute 'get'
2025-08-03 15:52:04,711 - bmad.example - INFO - [complete_example.py:154] - 
Using ArchitectAgent directly:
2025-08-03 15:52:06,663 - bmad.example - ERROR - [complete_example.py:178] - Error with ArchitectAgent: 'AgentRequest' object has no attribute 'get'
2025-08-03 15:52:06,670 - bmad.example - INFO - [complete_example.py:182] - 
=== Demonstrating Workflow Execution ===
2025-08-03 15:52:06,673 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:52:18,319 - bmad.example - INFO - [complete_example.py:198] - Starting brownfield fullstack workflow...
2025-08-03 15:52:18,320 - bmad.example - ERROR - [complete_example.py:220] - Workflow execution error: 'BrownfieldFullstackWorkflow' object has no attribute 'execute'
2025-08-03 15:52:18,334 - bmad.example - INFO - [complete_example.py:224] - 
=== Demonstrating Help System ===
2025-08-03 15:52:18,335 - bmad.example - INFO - [complete_example.py:235] - 
Help Query: How do I use the BMad agents system?
2025-08-03 15:52:22,162 - bmad.example - ERROR - [complete_example.py:250] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:52:22,163 - bmad.example - INFO - [complete_example.py:235] - 
Help Query: What agents are available?
2025-08-03 15:52:24,935 - bmad.example - ERROR - [complete_example.py:250] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:52:24,938 - bmad.example - INFO - [complete_example.py:235] - 
Help Query: How do I configure the system?
2025-08-03 15:52:27,700 - bmad.example - ERROR - [complete_example.py:250] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:52:27,702 - bmad.example - INFO - [complete_example.py:235] - 
Help Query: What workflows can I use?
2025-08-03 15:52:30,917 - bmad.example - ERROR - [complete_example.py:250] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:52:30,918 - bmad.example - INFO - [complete_example.py:235] - 
Help Query: How do I monitor performance?
2025-08-03 15:52:33,777 - bmad.example - ERROR - [complete_example.py:250] - Help system error: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:52:33,781 - bmad.example - INFO - [complete_example.py:286] - 
=== Demonstrating Error Handling ===
2025-08-03 15:52:33,783 - bmad.example - INFO - [complete_example.py:289] - 
Testing error handling with invalid request:
2025-08-03 15:52:37,453 - bmad.example - INFO - [complete_example.py:304] - [OK] Error handled correctly: AttributeError: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:52:37,454 - bmad.example - INFO - [complete_example.py:307] - 
Testing timeout handling:
2025-08-03 15:52:41,297 - bmad.example - INFO - [complete_example.py:322] - [OK] Timeout handled: AttributeError: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:52:41,298 - bmad.example - INFO - [complete_example.py:326] - 
=== Production Error Handling Demonstration ===
2025-08-03 15:52:41,298 - bmad - WARNING - [error_handling.py:39] - Attempt 1 failed, retrying in 0.5s: Simulated agent failure
2025-08-03 15:52:41,800 - bmad - WARNING - [error_handling.py:39] - Attempt 2 failed, retrying in 1.0s: Simulated agent failure
2025-08-03 15:52:42,809 - bmad - WARNING - [error_handling.py:39] - Attempt 3 failed, retrying in 2.0s: Simulated agent failure
2025-08-03 15:52:44,814 - bmad.example - INFO - [complete_example.py:338] - [OK] Retry operation result: Operation succeeded after retries
2025-08-03 15:52:45,823 - bmad.example - INFO - [complete_example.py:352] - [OK] Timeout handled correctly: Agent execution timed out after 1s
2025-08-03 15:52:45,823 - bmad.example - INFO - [complete_example.py:360] - Testing circuit breaker pattern:
2025-08-03 15:52:45,826 - bmad.example - INFO - [complete_example.py:365] -   Attempt 1: AgentExecutionError - Service unavailable
2025-08-03 15:52:45,827 - bmad.example - INFO - [complete_example.py:365] -   Attempt 2: AgentExecutionError - Service unavailable
2025-08-03 15:52:45,829 - bmad.example - INFO - [complete_example.py:365] -   Attempt 3: AgentExecutionError - Circuit breaker is open
2025-08-03 15:52:45,831 - bmad.example - INFO - [complete_example.py:367] -   [OK] Circuit breaker opened successfully
2025-08-03 15:52:45,832 - bmad.example - INFO - [complete_example.py:254] - 
=== Demonstrating Performance Monitoring ===
2025-08-03 15:52:45,835 - bmad.example - INFO - [complete_example.py:258] - Performance Summary:
2025-08-03 15:52:45,836 - bmad.example - INFO - [complete_example.py:259] -   Total operations: 0
2025-08-03 15:52:45,838 - bmad.example - INFO - [complete_example.py:260] -   Success rate: 0.00%
2025-08-03 15:52:45,839 - bmad.example - INFO - [complete_example.py:261] -   Average duration: 0.00s
2025-08-03 15:52:45,839 - bmad.example - INFO - [complete_example.py:262] -   Average memory usage: 0.0MB
2025-08-03 15:52:45,839 - bmad.example - INFO - [complete_example.py:271] - 
No recent errors found
2025-08-03 15:52:45,839 - bmad.example - INFO - [complete_example.py:413] - 
============================================================
2025-08-03 15:52:45,840 - bmad.example - INFO - [complete_example.py:414] - Complete example finished successfully!
2025-08-03 15:54:43,452 - bmad.example - INFO - [complete_example.py:394] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 15:54:43,453 - bmad.example - INFO - [complete_example.py:395] - ============================================================
2025-08-03 15:54:43,453 - bmad.example - INFO - [complete_example.py:60] - Initializing BMad Pydantic AI Agents system...
2025-08-03 15:54:45,678 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:54:45,678 - bmad.example - INFO - [complete_example.py:73] - System initialized successfully
2025-08-03 15:54:45,678 - bmad.example - INFO - [complete_example.py:374] - 
=== System Information ===
2025-08-03 15:54:45,678 - bmad.example - INFO - [complete_example.py:377] - Configuration:
2025-08-03 15:54:45,678 - bmad.example - INFO - [complete_example.py:379] -   default_model: gemini-2.5-flash
2025-08-03 15:54:45,679 - bmad.example - INFO - [complete_example.py:379] -   api_keys: {'google_ai': 'AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA', 'openai': '***********************************************************************************************', 'anthropic': '************************************************************************************************************'}
2025-08-03 15:54:45,679 - bmad.example - INFO - [complete_example.py:379] -   logging: {'level': 'INFO', 'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'}
2025-08-03 15:54:45,679 - bmad.example - INFO - [complete_example.py:379] -   state_management: {'enabled': True, 'state_dir': '.bmad_state', 'cleanup_days': 30}
2025-08-03 15:54:45,679 - bmad.example - INFO - [complete_example.py:379] -   performance: {'max_concurrent_agents': 5, 'default_timeout': 30, 'retry_attempts': 3}
2025-08-03 15:54:45,679 - bmad.example - INFO - [complete_example.py:379] -   markdownExploder: True
2025-08-03 15:54:45,679 - bmad.example - INFO - [complete_example.py:379] -   prd: {'prdFile': 'docs/prd.md', 'prdVersion': 'v4', 'prdSharded': True, 'prdShardedLocation': 'docs/prd', 'epicFilePattern': 'epic-{n}*.md'}
2025-08-03 15:54:45,680 - bmad.example - INFO - [complete_example.py:379] -   architecture: {'architectureFile': 'docs/architecture.md', 'architectureVersion': 'v4', 'architectureSharded': True, 'architectureShardedLocation': 'docs/architecture'}
2025-08-03 15:54:45,680 - bmad.example - INFO - [complete_example.py:379] -   customTechnicalDocuments: None
2025-08-03 15:54:45,680 - bmad.example - INFO - [complete_example.py:379] -   devLoadAlwaysFiles: ['docs/architecture/coding-standards.md', 'docs/architecture/tech-stack.md', 'docs/architecture/source-tree.md']
2025-08-03 15:54:45,680 - bmad.example - INFO - [complete_example.py:379] -   devDebugLog: .ai/debug-log.md
2025-08-03 15:54:45,680 - bmad.example - INFO - [complete_example.py:379] -   devStoryLocation: docs/stories
2025-08-03 15:54:45,680 - bmad.example - INFO - [complete_example.py:379] -   slashPrefix: BMad
2025-08-03 15:54:45,680 - bmad.example - INFO - [complete_example.py:379] -   project_path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:54:45,681 - bmad.example - INFO - [complete_example.py:379] -   log_level: INFO
2025-08-03 15:54:45,681 - bmad.example - INFO - [complete_example.py:379] -   enable_monitoring: True
2025-08-03 15:54:45,681 - bmad.example - INFO - [complete_example.py:379] -   max_concurrent_agents: 3
2025-08-03 15:54:45,681 - bmad.example - INFO - [complete_example.py:387] - 
System Status:
2025-08-03 15:54:45,681 - bmad.example - INFO - [complete_example.py:388] -   Project Path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:54:45,681 - bmad.example - INFO - [complete_example.py:389] -   Monitoring Enabled: True
2025-08-03 15:54:45,681 - bmad.example - INFO - [complete_example.py:390] -   Log Level: INFO
2025-08-03 15:54:45,682 - bmad.example - INFO - [complete_example.py:77] - 
=== Demonstrating Orchestrator Request Routing ===
2025-08-03 15:54:45,685 - bmad.example - INFO - [complete_example.py:96] - 
Request 1: Analyze the current codebase structure and identify technical debt
2025-08-03 15:54:47,570 - bmad.example - INFO - [complete_example.py:111] - Orchestrator action: Unknown
2025-08-03 15:54:47,570 - bmad.example - INFO - [complete_example.py:112] - Target agent: Unknown
2025-08-03 15:54:47,570 - bmad.example - INFO - [complete_example.py:113] - Message: AgentRunResult(output=OrchestrationResponse(action='route_to_agent', target_agent='architect', workflow_type=None, message='Routing your request to the `architect` agent to analyze the codebase struct...
2025-08-03 15:54:47,571 - bmad.example - ERROR - [complete_example.py:122] - Error processing request 1: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:54:47,571 - bmad.example - INFO - [complete_example.py:96] - 
Request 2: Design a microservices architecture for this monolithic application
2025-08-03 15:54:49,872 - bmad.example - INFO - [complete_example.py:111] - Orchestrator action: Unknown
2025-08-03 15:54:49,872 - bmad.example - INFO - [complete_example.py:112] - Target agent: Unknown
2025-08-03 15:54:49,873 - bmad.example - INFO - [complete_example.py:113] - Message: AgentRunResult(output=OrchestrationResponse(action='route_to_agent', target_agent='architect', workflow_type=None, message='Routing your request to the Architect agent to design a microservices archit...
2025-08-03 15:54:49,873 - bmad.example - ERROR - [complete_example.py:122] - Error processing request 2: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:54:49,873 - bmad.example - INFO - [complete_example.py:96] - 
Request 3: Help me understand how to use the BMad agents system
2025-08-03 15:54:54,845 - bmad.example - INFO - [complete_example.py:111] - Orchestrator action: Unknown
2025-08-03 15:54:54,846 - bmad.example - INFO - [complete_example.py:112] - Target agent: Unknown
2025-08-03 15:54:54,846 - bmad.example - INFO - [complete_example.py:113] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message='The BMad Agents system is designed to streamline software development tasks by levera...
2025-08-03 15:54:54,846 - bmad.example - ERROR - [complete_example.py:122] - Error processing request 3: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:54:54,846 - bmad.example - INFO - [complete_example.py:126] - 
=== Demonstrating Direct Agent Usage ===
2025-08-03 15:54:54,846 - bmad.example - INFO - [complete_example.py:129] - 
Using AnalystAgent directly:
2025-08-03 15:54:55,600 - bmad.analyst - INFO - [bmad_agent.py:47] - Processing request: analysis
2025-08-03 15:55:00,503 - bmad.analyst - INFO - [bmad_agent.py:61] - Request processed successfully
2025-08-03 15:55:00,504 - bmad.example - INFO - [complete_example.py:143] - Analyst Response: {'agent': 'analyst', 'timestamp': '2025-08-03T15:55:00.503413', 'status': 'success', 'result': RequirementAnalysis(summary="The request to 'Analyze the project structure and provide recommendations for improvement' is too broad. To deliver a comprehensive and relevant analysis, I require more specif...
2025-08-03 15:55:00,504 - bmad.example - INFO - [complete_example.py:152] - 
Using ArchitectAgent directly:
2025-08-03 15:55:01,270 - bmad.architect - INFO - [bmad_agent.py:47] - Processing request: design
2025-08-03 15:55:28,937 - bmad.architect - INFO - [bmad_agent.py:61] - Request processed successfully
2025-08-03 15:55:28,937 - bmad.example - INFO - [complete_example.py:167] - Architect Response: {'agent': 'architect', 'timestamp': '2025-08-03T15:55:28.937346', 'status': 'success', 'result': SystemArchitecture(overview='This architecture design for a brownfield e-commerce application adopts a hybrid approach, combining the existing monolithic system with new, scalable microservices. It lever...
2025-08-03 15:55:28,939 - bmad.example - INFO - [complete_example.py:177] - 
=== Demonstrating Workflow Execution ===
2025-08-03 15:55:28,940 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:55:33,445 - bmad.example - INFO - [complete_example.py:193] - Starting brownfield fullstack workflow...
2025-08-03 15:55:33,445 - bmad.example - ERROR - [complete_example.py:221] - Workflow execution error: 'StateManager' object has no attribute 'save_state'
2025-08-03 15:55:33,450 - bmad.example - INFO - [complete_example.py:225] - 
=== Demonstrating Help System ===
2025-08-03 15:55:33,451 - bmad.example - INFO - [complete_example.py:236] - 
Help Query: How do I use the BMad agents system?
2025-08-03 15:55:37,038 - bmad.example - INFO - [complete_example.py:248] - Action: Unknown
2025-08-03 15:55:37,039 - bmad.example - INFO - [complete_example.py:249] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message="Welcome to the BMad Method framework! I'm the Orchestrator. I help route your requests to specialized agents or initiate multi-agent workflows. You can specify what you need by describing your task, and I'll determine the best agent or workflow to handle it.\n\nHere are some of the specialized agents available:\n- **analyst**: For requirements analysis and user stories.\n- **architect**: For system design and technical decisions.\n- **pm**: For project planning and resource management.\n- **po**: For product vision and backlog management.\n- **sm**: For process facilitation and sprint planning.\n- **developer**: For code implementation and development tasks.\n- **qa**: For quality assurance and testing.\n- **ux**: For user experience design.\n- **devops**: For infrastructure and deployment.\n\nAnd here are the types of workflows I can initiate:\n- **brownfield-fullstack**: Enhance existing full-stack applications.\n- **greenfield-fullstack**: Create new full-stack applications.\n- **brownfield-service**: Enhance existing backend services.\n- **greenfield-service**: Create new backend services.\n- **brownfield-ui**: Enhance existing user interfaces.\n- **greenfield-ui**: Create new user interfaces.\n\n", next_steps=["To get started, please tell me what you need help with. For example, 'I need a new full-stack application for e-commerce' or 'I need help with a user story for a login feature.'"], context_updates={}))
2025-08-03 15:55:37,039 - bmad.example - INFO - [complete_example.py:236] - 
Help Query: What agents are available?
2025-08-03 15:55:40,160 - bmad.example - INFO - [complete_example.py:248] - Action: Unknown
2025-08-03 15:55:40,161 - bmad.example - INFO - [complete_example.py:249] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message='Here are the available agents and their roles:\n- **analyst**: Requirements analysis, user story creation, business analysis\n- **architect**: System design, technical decisions, architecture planning\n- **pm**: Project planning, resource management, timeline creation\n- **po**: Product vision, backlog management, stakeholder requirements\n- **sm**: Process facilitation, team coordination, sprint planning\n- **developer**: Code implementation, technical execution, development tasks\n- **qa**: Quality assurance, testing, bug tracking\n- **ux**: User experience design, usability, user research\n- **devops**: Infrastructure, deployment, CI/CD, monitoring\n\nHow else can I assist you? For example, you can ask about available workflows or how to start a specific workflow.', next_steps=['Ask about available workflows', 'Ask how to start a workflow', 'Specify your project needs'], context_updates={}))
2025-08-03 15:55:40,161 - bmad.example - INFO - [complete_example.py:236] - 
Help Query: How do I configure the system?
2025-08-03 15:55:43,038 - bmad.example - INFO - [complete_example.py:248] - Action: Unknown
2025-08-03 15:55:43,038 - bmad.example - INFO - [complete_example.py:249] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message='This is a general help request. To assist you with system configuration, please provide more specific details about what you are trying to configure, which system or application you are referring to, and what your goal is. If you are starting a new project, please describe your requirements so I can initiate the appropriate workflow.', next_steps=['Provide more specific details about your configuration needs.', "Describe your project requirements to initiate a new workflow (e.g., 'I want to build a new full-stack application for e-commerce')."], context_updates={}))
2025-08-03 15:55:43,039 - bmad.example - INFO - [complete_example.py:236] - 
Help Query: What workflows can I use?
2025-08-03 15:55:44,840 - bmad.example - INFO - [complete_example.py:248] - Action: Unknown
2025-08-03 15:55:44,840 - bmad.example - INFO - [complete_example.py:249] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message='You can initiate the following workflows: brownfield-fullstack, greenfield-fullstack, brownfield-service, greenfield-service, brownfield-ui, greenfield-ui. Each workflow is designed to address specific project needs, from enhancing existing applications to building new ones.', next_steps=["To start a workflow, please specify the type of project you are working on, e.g., 'Start a greenfield-fullstack workflow.'", "For more details on a specific workflow, you can ask, e.g., 'Tell me more about the brownfield-fullstack workflow.'"], context_updates={}))
2025-08-03 15:55:44,840 - bmad.example - INFO - [complete_example.py:236] - 
Help Query: How do I monitor performance?
2025-08-03 15:55:46,873 - bmad.example - INFO - [complete_example.py:248] - Action: Unknown
2025-08-03 15:55:46,874 - bmad.example - INFO - [complete_example.py:249] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message='Performance monitoring is typically handled by the DevOps agent. If you are looking to implement monitoring for a specific application or service, please provide more details.', next_steps=['You can specify the application/service you want to monitor.', 'You can ask to be connected to the DevOps agent for more detailed assistance.'], context_updates={}))
2025-08-03 15:55:46,874 - bmad.example - INFO - [complete_example.py:288] - 
=== Demonstrating Error Handling ===
2025-08-03 15:55:46,875 - bmad.example - INFO - [complete_example.py:291] - 
Testing error handling with invalid request:
2025-08-03 15:55:49,971 - bmad.example - INFO - [complete_example.py:306] - [OK] Error handled correctly: AttributeError: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:55:49,971 - bmad.example - INFO - [complete_example.py:309] - 
Testing timeout handling:
2025-08-03 15:55:55,845 - bmad.example - INFO - [complete_example.py:324] - [OK] Timeout handled: AttributeError: 'AgentRunResult' object has no attribute 'content'
2025-08-03 15:55:55,845 - bmad.example - INFO - [complete_example.py:328] - 
=== Production Error Handling Demonstration ===
2025-08-03 15:55:55,845 - bmad - WARNING - [error_handling.py:39] - Attempt 1 failed, retrying in 0.5s: Simulated agent failure
2025-08-03 15:55:56,355 - bmad - WARNING - [error_handling.py:39] - Attempt 2 failed, retrying in 1.0s: Simulated agent failure
2025-08-03 15:55:57,362 - bmad - WARNING - [error_handling.py:39] - Attempt 3 failed, retrying in 2.0s: Simulated agent failure
2025-08-03 15:55:59,377 - bmad - ERROR - [error_handling.py:44] - All 4 attempts failed: Simulated agent failure
2025-08-03 15:55:59,377 - bmad.example - INFO - [complete_example.py:342] - [OK] Retry operation failed after all attempts: Failed after 4 attempts
2025-08-03 15:56:00,388 - bmad.example - INFO - [complete_example.py:354] - [OK] Timeout handled correctly: Agent execution timed out after 1s
2025-08-03 15:56:00,389 - bmad.example - INFO - [complete_example.py:362] - Testing circuit breaker pattern:
2025-08-03 15:56:00,389 - bmad.example - INFO - [complete_example.py:367] -   Attempt 1: AgentExecutionError - Service unavailable
2025-08-03 15:56:00,389 - bmad.example - INFO - [complete_example.py:367] -   Attempt 2: AgentExecutionError - Service unavailable
2025-08-03 15:56:00,389 - bmad.example - INFO - [complete_example.py:367] -   Attempt 3: AgentExecutionError - Circuit breaker is open
2025-08-03 15:56:00,389 - bmad.example - INFO - [complete_example.py:369] -   [OK] Circuit breaker opened successfully
2025-08-03 15:56:00,390 - bmad.example - INFO - [complete_example.py:256] - 
=== Demonstrating Performance Monitoring ===
2025-08-03 15:56:00,390 - bmad.example - INFO - [complete_example.py:260] - Performance Summary:
2025-08-03 15:56:00,390 - bmad.example - INFO - [complete_example.py:261] -   Total operations: 0
2025-08-03 15:56:00,390 - bmad.example - INFO - [complete_example.py:262] -   Success rate: 0.00%
2025-08-03 15:56:00,390 - bmad.example - INFO - [complete_example.py:263] -   Average duration: 0.00s
2025-08-03 15:56:00,391 - bmad.example - INFO - [complete_example.py:264] -   Average memory usage: 0.0MB
2025-08-03 15:56:00,391 - bmad.example - INFO - [complete_example.py:273] - 
No recent errors found
2025-08-03 15:56:00,391 - bmad.example - INFO - [complete_example.py:415] - 
============================================================
2025-08-03 15:56:00,391 - bmad.example - INFO - [complete_example.py:416] - Complete example finished successfully!
2025-08-03 15:59:11,463 - bmad.example - INFO - [complete_example.py:394] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 15:59:11,464 - bmad.example - INFO - [complete_example.py:395] - ============================================================
2025-08-03 15:59:11,464 - bmad.example - INFO - [complete_example.py:60] - Initializing BMad Pydantic AI Agents system...
2025-08-03 15:59:13,658 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 15:59:13,658 - bmad.example - INFO - [complete_example.py:73] - System initialized successfully
2025-08-03 15:59:13,658 - bmad.example - INFO - [complete_example.py:374] - 
=== System Information ===
2025-08-03 15:59:13,658 - bmad.example - INFO - [complete_example.py:377] - Configuration:
2025-08-03 15:59:13,658 - bmad.example - INFO - [complete_example.py:379] -   default_model: gemini-2.5-flash
2025-08-03 15:59:13,659 - bmad.example - INFO - [complete_example.py:379] -   api_keys: {'google_ai': 'AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA', 'openai': '***********************************************************************************************', 'anthropic': '************************************************************************************************************'}
2025-08-03 15:59:13,659 - bmad.example - INFO - [complete_example.py:379] -   logging: {'level': 'INFO', 'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'}
2025-08-03 15:59:13,659 - bmad.example - INFO - [complete_example.py:379] -   state_management: {'enabled': True, 'state_dir': '.bmad_state', 'cleanup_days': 30}
2025-08-03 15:59:13,659 - bmad.example - INFO - [complete_example.py:379] -   performance: {'max_concurrent_agents': 5, 'default_timeout': 30, 'retry_attempts': 3}
2025-08-03 15:59:13,659 - bmad.example - INFO - [complete_example.py:379] -   markdownExploder: True
2025-08-03 15:59:13,659 - bmad.example - INFO - [complete_example.py:379] -   prd: {'prdFile': 'docs/prd.md', 'prdVersion': 'v4', 'prdSharded': True, 'prdShardedLocation': 'docs/prd', 'epicFilePattern': 'epic-{n}*.md'}
2025-08-03 15:59:13,659 - bmad.example - INFO - [complete_example.py:379] -   architecture: {'architectureFile': 'docs/architecture.md', 'architectureVersion': 'v4', 'architectureSharded': True, 'architectureShardedLocation': 'docs/architecture'}
2025-08-03 15:59:13,660 - bmad.example - INFO - [complete_example.py:379] -   customTechnicalDocuments: None
2025-08-03 15:59:13,660 - bmad.example - INFO - [complete_example.py:379] -   devLoadAlwaysFiles: ['docs/architecture/coding-standards.md', 'docs/architecture/tech-stack.md', 'docs/architecture/source-tree.md']
2025-08-03 15:59:13,660 - bmad.example - INFO - [complete_example.py:379] -   devDebugLog: .ai/debug-log.md
2025-08-03 15:59:13,660 - bmad.example - INFO - [complete_example.py:379] -   devStoryLocation: docs/stories
2025-08-03 15:59:13,660 - bmad.example - INFO - [complete_example.py:379] -   slashPrefix: BMad
2025-08-03 15:59:13,660 - bmad.example - INFO - [complete_example.py:379] -   project_path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:59:13,661 - bmad.example - INFO - [complete_example.py:379] -   log_level: INFO
2025-08-03 15:59:13,661 - bmad.example - INFO - [complete_example.py:379] -   enable_monitoring: True
2025-08-03 15:59:13,661 - bmad.example - INFO - [complete_example.py:379] -   max_concurrent_agents: 3
2025-08-03 15:59:13,661 - bmad.example - INFO - [complete_example.py:387] - 
System Status:
2025-08-03 15:59:13,661 - bmad.example - INFO - [complete_example.py:388] -   Project Path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 15:59:13,661 - bmad.example - INFO - [complete_example.py:389] -   Monitoring Enabled: True
2025-08-03 15:59:13,661 - bmad.example - INFO - [complete_example.py:390] -   Log Level: INFO
2025-08-03 15:59:13,661 - bmad.example - INFO - [complete_example.py:77] - 
=== Demonstrating Orchestrator Request Routing ===
2025-08-03 15:59:13,662 - bmad.example - INFO - [complete_example.py:96] - 
Request 1: Analyze the current codebase structure and identify technical debt
2025-08-03 15:59:17,946 - bmad.example - INFO - [complete_example.py:111] - Orchestrator action: Unknown
2025-08-03 15:59:17,947 - bmad.example - INFO - [complete_example.py:112] - Target agent: Unknown
2025-08-03 15:59:17,947 - bmad.example - INFO - [complete_example.py:113] - Message: AgentRunResult(output=OrchestrationResponse(action='route_to_agent', target_agent='architect', workflow_type=None, message="I understand you'd like to analyze the codebase structure and identify techn...
2025-08-03 15:59:17,948 - bmad.example - ERROR - [complete_example.py:122] - Error processing request 1: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:59:17,948 - bmad.example - INFO - [complete_example.py:96] - 
Request 2: Design a microservices architecture for this monolithic application
2025-08-03 15:59:19,695 - bmad.example - INFO - [complete_example.py:111] - Orchestrator action: Unknown
2025-08-03 15:59:19,695 - bmad.example - INFO - [complete_example.py:112] - Target agent: Unknown
2025-08-03 15:59:19,695 - bmad.example - INFO - [complete_example.py:113] - Message: AgentRunResult(output=OrchestrationResponse(action='route_to_agent', target_agent='architect', workflow_type=None, message='Routing your request to the `architect` agent for designing a microservices ...
2025-08-03 15:59:19,696 - bmad.example - ERROR - [complete_example.py:122] - Error processing request 2: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:59:19,696 - bmad.example - INFO - [complete_example.py:96] - 
Request 3: Help me understand how to use the BMad agents system
2025-08-03 15:59:22,865 - bmad.example - INFO - [complete_example.py:111] - Orchestrator action: Unknown
2025-08-03 15:59:22,865 - bmad.example - INFO - [complete_example.py:112] - Target agent: Unknown
2025-08-03 15:59:22,866 - bmad.example - INFO - [complete_example.py:113] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message="Hello! I can help you understand how to use the BMad agents system. You can initiate ...
2025-08-03 15:59:22,866 - bmad.example - ERROR - [complete_example.py:122] - Error processing request 3: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 15:59:22,866 - bmad.example - INFO - [complete_example.py:126] - 
=== Demonstrating Direct Agent Usage ===
2025-08-03 15:59:22,866 - bmad.example - INFO - [complete_example.py:129] - 
Using AnalystAgent directly:
2025-08-03 15:59:23,618 - bmad.analyst - INFO - [bmad_agent.py:47] - Processing request: analysis
2025-08-03 15:59:27,467 - bmad.analyst - INFO - [bmad_agent.py:61] - Request processed successfully
2025-08-03 15:59:27,467 - bmad.example - INFO - [complete_example.py:143] - Analyst Response: {'agent': 'analyst', 'timestamp': '2025-08-03T15:59:27.466267', 'status': 'success', 'result': RequirementAnalysis(summary="The request asks for an analysis of 'project structure' and recommendations for improvement. This is a very broad request. To provide a meaningful and actionable analysis, more...
2025-08-03 15:59:27,467 - bmad.example - INFO - [complete_example.py:152] - 
Using ArchitectAgent directly:
2025-08-03 15:59:28,218 - bmad.architect - INFO - [bmad_agent.py:47] - Processing request: design
2025-08-03 16:00:52,724 - bmad.example - INFO - [complete_example.py:394] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 16:00:52,725 - bmad.example - INFO - [complete_example.py:395] - ============================================================
2025-08-03 16:00:52,725 - bmad.example - INFO - [complete_example.py:60] - Initializing BMad Pydantic AI Agents system...
2025-08-03 16:00:54,933 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 16:00:54,933 - bmad.example - INFO - [complete_example.py:73] - System initialized successfully
2025-08-03 16:00:54,934 - bmad.example - INFO - [complete_example.py:374] - 
=== System Information ===
2025-08-03 16:00:54,934 - bmad.example - INFO - [complete_example.py:377] - Configuration:
2025-08-03 16:00:54,934 - bmad.example - INFO - [complete_example.py:379] -   default_model: gemini-2.5-flash
2025-08-03 16:00:54,934 - bmad.example - INFO - [complete_example.py:379] -   api_keys: {'google_ai': 'AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA', 'openai': '***********************************************************************************************', 'anthropic': '************************************************************************************************************'}
2025-08-03 16:00:54,934 - bmad.example - INFO - [complete_example.py:379] -   logging: {'level': 'INFO', 'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'}
2025-08-03 16:00:54,934 - bmad.example - INFO - [complete_example.py:379] -   state_management: {'enabled': True, 'state_dir': '.bmad_state', 'cleanup_days': 30}
2025-08-03 16:00:54,935 - bmad.example - INFO - [complete_example.py:379] -   performance: {'max_concurrent_agents': 5, 'default_timeout': 30, 'retry_attempts': 3}
2025-08-03 16:00:54,935 - bmad.example - INFO - [complete_example.py:379] -   markdownExploder: True
2025-08-03 16:00:54,935 - bmad.example - INFO - [complete_example.py:379] -   prd: {'prdFile': 'docs/prd.md', 'prdVersion': 'v4', 'prdSharded': True, 'prdShardedLocation': 'docs/prd', 'epicFilePattern': 'epic-{n}*.md'}
2025-08-03 16:00:54,935 - bmad.example - INFO - [complete_example.py:379] -   architecture: {'architectureFile': 'docs/architecture.md', 'architectureVersion': 'v4', 'architectureSharded': True, 'architectureShardedLocation': 'docs/architecture'}
2025-08-03 16:00:54,935 - bmad.example - INFO - [complete_example.py:379] -   customTechnicalDocuments: None
2025-08-03 16:00:54,935 - bmad.example - INFO - [complete_example.py:379] -   devLoadAlwaysFiles: ['docs/architecture/coding-standards.md', 'docs/architecture/tech-stack.md', 'docs/architecture/source-tree.md']
2025-08-03 16:00:54,935 - bmad.example - INFO - [complete_example.py:379] -   devDebugLog: .ai/debug-log.md
2025-08-03 16:00:54,935 - bmad.example - INFO - [complete_example.py:379] -   devStoryLocation: docs/stories
2025-08-03 16:00:54,936 - bmad.example - INFO - [complete_example.py:379] -   slashPrefix: BMad
2025-08-03 16:00:54,936 - bmad.example - INFO - [complete_example.py:379] -   project_path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 16:00:54,936 - bmad.example - INFO - [complete_example.py:379] -   log_level: INFO
2025-08-03 16:00:54,936 - bmad.example - INFO - [complete_example.py:379] -   enable_monitoring: True
2025-08-03 16:00:54,936 - bmad.example - INFO - [complete_example.py:379] -   max_concurrent_agents: 3
2025-08-03 16:00:54,936 - bmad.example - INFO - [complete_example.py:387] - 
System Status:
2025-08-03 16:00:54,936 - bmad.example - INFO - [complete_example.py:388] -   Project Path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 16:00:54,936 - bmad.example - INFO - [complete_example.py:389] -   Monitoring Enabled: True
2025-08-03 16:00:54,937 - bmad.example - INFO - [complete_example.py:390] -   Log Level: INFO
2025-08-03 16:00:54,937 - bmad.example - INFO - [complete_example.py:77] - 
=== Demonstrating Orchestrator Request Routing ===
2025-08-03 16:00:54,937 - bmad.example - INFO - [complete_example.py:96] - 
Request 1: Analyze the current codebase structure and identify technical debt
2025-08-03 16:00:58,196 - bmad.example - INFO - [complete_example.py:111] - Orchestrator action: Unknown
2025-08-03 16:00:58,196 - bmad.example - INFO - [complete_example.py:112] - Target agent: Unknown
2025-08-03 16:00:58,196 - bmad.example - INFO - [complete_example.py:113] - Message: AgentRunResult(output=OrchestrationResponse(action='route_to_agent', target_agent='architect', workflow_type=None, message='Routing your request to the `architect` agent to analyze the codebase struct...
2025-08-03 16:00:58,197 - bmad.example - ERROR - [complete_example.py:122] - Error processing request 1: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 16:00:58,197 - bmad.example - INFO - [complete_example.py:96] - 
Request 2: Design a microservices architecture for this monolithic application
2025-08-03 16:01:01,368 - bmad.example - INFO - [complete_example.py:111] - Orchestrator action: Unknown
2025-08-03 16:01:01,369 - bmad.example - INFO - [complete_example.py:112] - Target agent: Unknown
2025-08-03 16:01:01,369 - bmad.example - INFO - [complete_example.py:113] - Message: AgentRunResult(output=OrchestrationResponse(action='route_to_agent', target_agent='architect', workflow_type=None, message='Routing your request to the Architect agent to design the microservices arch...
2025-08-03 16:01:01,369 - bmad.example - ERROR - [complete_example.py:122] - Error processing request 2: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 16:01:01,369 - bmad.example - INFO - [complete_example.py:96] - 
Request 3: Help me understand how to use the BMad agents system
2025-08-03 16:01:04,957 - bmad.example - INFO - [complete_example.py:111] - Orchestrator action: Unknown
2025-08-03 16:01:04,957 - bmad.example - INFO - [complete_example.py:112] - Target agent: Unknown
2025-08-03 16:01:04,958 - bmad.example - INFO - [complete_example.py:113] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message='The BMad Agents system is designed to streamline software development by leveraging s...
2025-08-03 16:01:04,958 - bmad.example - ERROR - [complete_example.py:122] - Error processing request 3: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 16:01:04,958 - bmad.example - INFO - [complete_example.py:126] - 
=== Demonstrating Direct Agent Usage ===
2025-08-03 16:01:04,959 - bmad.example - INFO - [complete_example.py:129] - 
Using AnalystAgent directly:
2025-08-03 16:01:05,717 - bmad.analyst - INFO - [bmad_agent.py:47] - Processing request: analysis
2025-08-03 16:01:08,733 - bmad.analyst - INFO - [bmad_agent.py:61] - Request processed successfully
2025-08-03 16:01:08,734 - bmad.example - INFO - [complete_example.py:143] - Analyst Response: {'agent': 'analyst', 'timestamp': '2025-08-03T16:01:08.733316', 'status': 'success', 'result': RequirementAnalysis(summary='To analyze the project structure and provide recommendations for improvement, I require more specific information about which aspect of the project structure needs to be analyz...
2025-08-03 16:01:08,735 - bmad.example - INFO - [complete_example.py:152] - 
Using ArchitectAgent directly:
2025-08-03 16:01:09,521 - bmad.architect - INFO - [bmad_agent.py:47] - Processing request: design
2025-08-03 16:01:39,445 - bmad.architect - INFO - [bmad_agent.py:61] - Request processed successfully
2025-08-03 16:01:39,445 - bmad.example - INFO - [complete_example.py:167] - Architect Response: {'agent': 'architect', 'timestamp': '2025-08-03T16:01:39.445284', 'status': 'success', 'result': SystemArchitecture(overview="This architecture design focuses on transforming a brownfield e-commerce application into a highly scalable, resilient, and maintainable system. The primary approach involves...
2025-08-03 16:01:39,447 - bmad.example - INFO - [complete_example.py:177] - 
=== Demonstrating Workflow Execution ===
2025-08-03 16:01:39,448 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 16:01:43,970 - bmad.example - INFO - [complete_example.py:193] - Starting brownfield fullstack workflow...
2025-08-03 16:01:43,973 - bmad.state_manager - INFO - [state_manager.py:40] - Workflow state saved: a0edfdf4-7e01-4d71-a1ad-a14bb572d66a
2025-08-03 16:01:43,973 - bmad.example - INFO - [complete_example.py:203] - Workflow started: a0edfdf4-7e01-4d71-a1ad-a14bb572d66a
2025-08-03 16:01:43,974 - bmad.example - INFO - [complete_example.py:204] - Current step: scope_classification
2025-08-03 16:01:43,974 - bmad.state_manager - ERROR - [state_manager.py:72] - Failed to load workflow state a0edfdf4-7e01-4d71-a1ad-a14bb572d66a: 5 validation errors for WorkflowState
progress
  Field required [type=missing, input_value={'workflow_id': 'a0edfdf4...82), 'status': 'active'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
started_at
  Field required [type=missing, input_value={'workflow_id': 'a0edfdf4...82), 'status': 'active'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
context
  Field required [type=missing, input_value={'workflow_id': 'a0edfdf4...82), 'status': 'active'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
participants
  Field required [type=missing, input_value={'workflow_id': 'a0edfdf4...82), 'status': 'active'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
pending_steps
  Field required [type=missing, input_value={'workflow_id': 'a0edfdf4...82), 'status': 'active'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-03 16:01:43,974 - bmad.example - ERROR - [complete_example.py:221] - Workflow execution error: Workflow a0edfdf4-7e01-4d71-a1ad-a14bb572d66a not found
2025-08-03 16:01:43,980 - bmad.example - INFO - [complete_example.py:225] - 
=== Demonstrating Help System ===
2025-08-03 16:01:43,980 - bmad.example - INFO - [complete_example.py:236] - 
Help Query: How do I use the BMad agents system?
2025-08-03 16:01:47,991 - bmad.example - INFO - [complete_example.py:248] - Action: Unknown
2025-08-03 16:01:47,992 - bmad.example - INFO - [complete_example.py:249] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message="Welcome to the BMad Method framework! I am the BMad Orchestrator, your central coordination agent. I can route your requests to specialized agents or initiate multi-agent workflows.\n\nHere are the available agents and their roles:\n- **analyst**: Requirements analysis, user story creation, business analysis\n- **architect**: System design, technical decisions, architecture planning\n- **pm**: Project planning, resource management, timeline creation\n- **po**: Product vision, backlog management, stakeholder requirements\n- **sm**: Process facilitation, team coordination, sprint planning\n- **developer**: Code implementation, technical execution, development tasks\n- **qa**: Quality assurance, testing, bug tracking\n- **ux**: User experience design, usability, user research\n- **devops**: Infrastructure, deployment, CI/CD, monitoring\n\nAnd here are the available workflows:\n- **brownfield-fullstack**: Enhance existing full-stack applications\n- **greenfield-fullstack**: Create new full-stack applications\n- **brownfield-service**: Enhance existing backend services\n- **greenfield-service**: Create new backend services\n- **brownfield-ui**: Enhance existing user interfaces\n- **greenfield-ui**: Create new user interfaces\n\nHow can I assist you today? Please tell me what you'd like to achieve, and I can either route you to a specific agent or start a relevant workflow.", next_steps=["To request a specific agent, please state the agent's name (e.g., 'I need the analyst to help with requirements').", "To start a workflow, please specify the workflow type (e.g., 'Start a greenfield-fullstack workflow').", 'Describe your task or goal, and I will suggest the best way forward.'], context_updates={}))
2025-08-03 16:01:47,993 - bmad.example - INFO - [complete_example.py:236] - 
Help Query: What agents are available?
2025-08-03 16:01:50,209 - bmad.example - INFO - [complete_example.py:248] - Action: Unknown
2025-08-03 16:01:50,209 - bmad.example - INFO - [complete_example.py:249] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message='Here are the agents available: analyst, architect, pm, po, sm, developer, qa, ux, devops.', next_steps=[], context_updates={}))
2025-08-03 16:01:50,209 - bmad.example - INFO - [complete_example.py:236] - 
Help Query: How do I configure the system?
2025-08-03 16:01:53,647 - bmad.example - INFO - [complete_example.py:248] - Action: Unknown
2025-08-03 16:01:53,647 - bmad.example - INFO - [complete_example.py:249] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message="To help you configure the system, please provide more details about which system you are referring to, what specific configurations you are trying to achieve, and any error messages you might be encountering. This will allow me to route your request to the most appropriate agent or provide more specific guidance. You can also refer to the system's official documentation for detailed configuration instructions.", next_steps=['Provide more details about the system and configuration task.', 'Specify if you need help with a new setup (greenfield) or modifying an existing one (brownfield).'], context_updates={}))
2025-08-03 16:01:53,647 - bmad.example - INFO - [complete_example.py:236] - 
Help Query: What workflows can I use?
2025-08-03 16:01:55,910 - bmad.example - INFO - [complete_example.py:248] - Action: Unknown
2025-08-03 16:01:55,911 - bmad.example - INFO - [complete_example.py:249] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message='You can initiate the following workflows: brownfield-fullstack, greenfield-fullstack, brownfield-service, greenfield-service, brownfield-ui, greenfield-ui. What would you like to build or enhance today?', next_steps=['Specify the type of application (full-stack, service, or UI).', "Indicate if it's a new application (greenfield) or an existing one (brownfield)."], context_updates={}))
2025-08-03 16:01:55,911 - bmad.example - INFO - [complete_example.py:236] - 
Help Query: How do I monitor performance?
2025-08-03 16:01:58,417 - bmad.example - INFO - [complete_example.py:248] - Action: Unknown
2025-08-03 16:01:58,417 - bmad.example - INFO - [complete_example.py:249] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message='To monitor performance, you would typically interact with the `devops` agent. The `devops` agent is responsible for infrastructure, deployment, CI/CD, and monitoring, which includes performance monitoring. Would you like to initiate a conversation with the `devops` agent?', next_steps=['You can specify a request for the `devops` agent for performance monitoring.'], context_updates={}))
2025-08-03 16:01:58,418 - bmad.example - INFO - [complete_example.py:288] - 
=== Demonstrating Error Handling ===
2025-08-03 16:01:58,418 - bmad.example - INFO - [complete_example.py:291] - 
Testing error handling with invalid request:
2025-08-03 16:02:01,014 - bmad.example - INFO - [complete_example.py:306] - [OK] Error handled correctly: AttributeError: 'AgentRunResult' object has no attribute 'content'
2025-08-03 16:02:01,014 - bmad.example - INFO - [complete_example.py:309] - 
Testing timeout handling:
2025-08-03 16:02:05,353 - bmad.example - INFO - [complete_example.py:324] - [OK] Timeout handled: AttributeError: 'AgentRunResult' object has no attribute 'content'
2025-08-03 16:02:05,353 - bmad.example - INFO - [complete_example.py:328] - 
=== Production Error Handling Demonstration ===
2025-08-03 16:02:05,353 - bmad - WARNING - [error_handling.py:39] - Attempt 1 failed, retrying in 0.5s: Simulated agent failure
2025-08-03 16:02:05,863 - bmad.example - INFO - [complete_example.py:340] - [OK] Retry operation result: Operation succeeded after retries
2025-08-03 16:02:06,874 - bmad.example - INFO - [complete_example.py:354] - [OK] Timeout handled correctly: Agent execution timed out after 1s
2025-08-03 16:02:06,875 - bmad.example - INFO - [complete_example.py:362] - Testing circuit breaker pattern:
2025-08-03 16:02:06,875 - bmad.example - INFO - [complete_example.py:367] -   Attempt 1: AgentExecutionError - Service unavailable
2025-08-03 16:02:06,875 - bmad.example - INFO - [complete_example.py:367] -   Attempt 2: AgentExecutionError - Service unavailable
2025-08-03 16:02:06,875 - bmad.example - INFO - [complete_example.py:367] -   Attempt 3: AgentExecutionError - Circuit breaker is open
2025-08-03 16:02:06,876 - bmad.example - INFO - [complete_example.py:369] -   [OK] Circuit breaker opened successfully
2025-08-03 16:02:06,876 - bmad.example - INFO - [complete_example.py:256] - 
=== Demonstrating Performance Monitoring ===
2025-08-03 16:02:06,876 - bmad.example - INFO - [complete_example.py:260] - Performance Summary:
2025-08-03 16:02:06,876 - bmad.example - INFO - [complete_example.py:261] -   Total operations: 0
2025-08-03 16:02:06,876 - bmad.example - INFO - [complete_example.py:262] -   Success rate: 0.00%
2025-08-03 16:02:06,876 - bmad.example - INFO - [complete_example.py:263] -   Average duration: 0.00s
2025-08-03 16:02:06,876 - bmad.example - INFO - [complete_example.py:264] -   Average memory usage: 0.0MB
2025-08-03 16:02:06,877 - bmad.example - INFO - [complete_example.py:273] - 
No recent errors found
2025-08-03 16:02:06,877 - bmad.example - INFO - [complete_example.py:415] - 
============================================================
2025-08-03 16:02:06,877 - bmad.example - INFO - [complete_example.py:416] - Complete example finished successfully!
2025-08-03 16:02:34,787 - bmad.example - INFO - [complete_example.py:394] - Starting BMad Pydantic AI Agents Complete Example
2025-08-03 16:02:34,787 - bmad.example - INFO - [complete_example.py:395] - ============================================================
2025-08-03 16:02:34,788 - bmad.example - INFO - [complete_example.py:60] - Initializing BMad Pydantic AI Agents system...
2025-08-03 16:02:36,964 - bmad.state_manager - INFO - [state_manager.py:25] - StateManager initialized with state directory: .bmad_state
2025-08-03 16:02:36,964 - bmad.example - INFO - [complete_example.py:73] - System initialized successfully
2025-08-03 16:02:36,965 - bmad.example - INFO - [complete_example.py:374] - 
=== System Information ===
2025-08-03 16:02:36,965 - bmad.example - INFO - [complete_example.py:377] - Configuration:
2025-08-03 16:02:36,965 - bmad.example - INFO - [complete_example.py:379] -   default_model: gemini-2.5-flash
2025-08-03 16:02:36,965 - bmad.example - INFO - [complete_example.py:379] -   api_keys: {'google_ai': 'AIzaSyBWKWSbWhAe3GrSI0ko1MVOKvvRt70UosA', 'openai': '***********************************************************************************************', 'anthropic': '************************************************************************************************************'}
2025-08-03 16:02:36,965 - bmad.example - INFO - [complete_example.py:379] -   logging: {'level': 'INFO', 'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'}
2025-08-03 16:02:36,965 - bmad.example - INFO - [complete_example.py:379] -   state_management: {'enabled': True, 'state_dir': '.bmad_state', 'cleanup_days': 30}
2025-08-03 16:02:36,966 - bmad.example - INFO - [complete_example.py:379] -   performance: {'max_concurrent_agents': 5, 'default_timeout': 30, 'retry_attempts': 3}
2025-08-03 16:02:36,966 - bmad.example - INFO - [complete_example.py:379] -   markdownExploder: True
2025-08-03 16:02:36,966 - bmad.example - INFO - [complete_example.py:379] -   prd: {'prdFile': 'docs/prd.md', 'prdVersion': 'v4', 'prdSharded': True, 'prdShardedLocation': 'docs/prd', 'epicFilePattern': 'epic-{n}*.md'}
2025-08-03 16:02:36,966 - bmad.example - INFO - [complete_example.py:379] -   architecture: {'architectureFile': 'docs/architecture.md', 'architectureVersion': 'v4', 'architectureSharded': True, 'architectureShardedLocation': 'docs/architecture'}
2025-08-03 16:02:36,966 - bmad.example - INFO - [complete_example.py:379] -   customTechnicalDocuments: None
2025-08-03 16:02:36,966 - bmad.example - INFO - [complete_example.py:379] -   devLoadAlwaysFiles: ['docs/architecture/coding-standards.md', 'docs/architecture/tech-stack.md', 'docs/architecture/source-tree.md']
2025-08-03 16:02:36,966 - bmad.example - INFO - [complete_example.py:379] -   devDebugLog: .ai/debug-log.md
2025-08-03 16:02:36,966 - bmad.example - INFO - [complete_example.py:379] -   devStoryLocation: docs/stories
2025-08-03 16:02:36,967 - bmad.example - INFO - [complete_example.py:379] -   slashPrefix: BMad
2025-08-03 16:02:36,967 - bmad.example - INFO - [complete_example.py:379] -   project_path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 16:02:36,967 - bmad.example - INFO - [complete_example.py:379] -   log_level: INFO
2025-08-03 16:02:36,967 - bmad.example - INFO - [complete_example.py:379] -   enable_monitoring: True
2025-08-03 16:02:36,967 - bmad.example - INFO - [complete_example.py:379] -   max_concurrent_agents: 3
2025-08-03 16:02:36,967 - bmad.example - INFO - [complete_example.py:387] - 
System Status:
2025-08-03 16:02:36,967 - bmad.example - INFO - [complete_example.py:388] -   Project Path: C:\Users\<USER>\OneDrive\Documents\VCode projects\BMADPydanticAgents
2025-08-03 16:02:36,967 - bmad.example - INFO - [complete_example.py:389] -   Monitoring Enabled: True
2025-08-03 16:02:36,968 - bmad.example - INFO - [complete_example.py:390] -   Log Level: INFO
2025-08-03 16:02:36,968 - bmad.example - INFO - [complete_example.py:77] - 
=== Demonstrating Orchestrator Request Routing ===
2025-08-03 16:02:36,968 - bmad.example - INFO - [complete_example.py:96] - 
Request 1: Analyze the current codebase structure and identify technical debt
2025-08-03 16:02:39,475 - bmad.example - INFO - [complete_example.py:111] - Orchestrator action: Unknown
2025-08-03 16:02:39,475 - bmad.example - INFO - [complete_example.py:112] - Target agent: Unknown
2025-08-03 16:02:39,475 - bmad.example - INFO - [complete_example.py:113] - Message: AgentRunResult(output=OrchestrationResponse(action='route_to_agent', target_agent='architect', workflow_type=None, message='Routing your request to the `architect` agent to analyze the codebase struct...
2025-08-03 16:02:39,475 - bmad.example - ERROR - [complete_example.py:122] - Error processing request 1: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 16:02:39,476 - bmad.example - INFO - [complete_example.py:96] - 
Request 2: Design a microservices architecture for this monolithic application
2025-08-03 16:02:41,681 - bmad.example - INFO - [complete_example.py:111] - Orchestrator action: Unknown
2025-08-03 16:02:41,682 - bmad.example - INFO - [complete_example.py:112] - Target agent: Unknown
2025-08-03 16:02:41,682 - bmad.example - INFO - [complete_example.py:113] - Message: AgentRunResult(output=OrchestrationResponse(action='route_to_agent', target_agent='architect', workflow_type=None, message='Routing your request to the Architect agent to design the microservices arch...
2025-08-03 16:02:41,682 - bmad.example - ERROR - [complete_example.py:122] - Error processing request 2: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 16:02:41,682 - bmad.example - INFO - [complete_example.py:96] - 
Request 3: Help me understand how to use the BMad agents system
2025-08-03 16:02:44,843 - bmad.example - INFO - [complete_example.py:111] - Orchestrator action: Unknown
2025-08-03 16:02:44,844 - bmad.example - INFO - [complete_example.py:112] - Target agent: Unknown
2025-08-03 16:02:44,844 - bmad.example - INFO - [complete_example.py:113] - Message: AgentRunResult(output=OrchestrationResponse(action='provide_help', target_agent=None, workflow_type=None, message="The BMad (Business-driven, Model-based, Agile, and DevOps) Method is a structured app...
2025-08-03 16:02:44,844 - bmad.example - ERROR - [complete_example.py:122] - Error processing request 3: 'AgentRunResult' object has no attribute 'agent_id'
2025-08-03 16:02:44,845 - bmad.example - INFO - [complete_example.py:126] - 
=== Demonstrating Direct Agent Usage ===
2025-08-03 16:02:44,845 - bmad.example - INFO - [complete_example.py:129] - 
Using AnalystAgent directly:
2025-08-03 16:02:45,594 - bmad.analyst - INFO - [bmad_agent.py:47] - Processing request: analysis
2025-08-03 16:02:50,279 - bmad.analyst - INFO - [bmad_agent.py:61] - Request processed successfully
2025-08-03 16:02:50,279 - bmad.example - INFO - [complete_example.py:143] - Analyst Response: {'agent': 'analyst', 'timestamp': '2025-08-03T16:02:50.279024', 'status': 'success', 'result': RequirementAnalysis(summary="The request is to analyze the project structure and provide recommendations for improvement. However, the term 'project structure' is broad and requires further clarification t...
2025-08-03 16:02:50,280 - bmad.example - INFO - [complete_example.py:152] - 
Using ArchitectAgent directly:
2025-08-03 16:02:51,027 - bmad.architect - INFO - [bmad_agent.py:47] - Processing request: design
