import logging
import logging.handlers
from pathlib import Path
from typing import Optional, Dict, Any
import sys
from datetime import datetime

class BMadLogger:
    """
    Centralized logging configuration for BMad agents system.
    Provides structured logging with different levels and outputs.
    """
    
    def __init__(self, log_dir: str = "logs", log_level: str = "INFO"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        self.log_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # Create formatters
        self.detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        )
        
        self.simple_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        self.json_formatter = JsonFormatter()
        
        # Configure root logger
        self._setup_root_logger()
        
        # Setup specific loggers
        self._setup_agent_loggers()
        self._setup_workflow_logger()
        self._setup_system_logger()
    
    def _setup_root_logger(self):
        """Setup the root logger configuration."""
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(self.simple_formatter)
        root_logger.addHandler(console_handler)
    
    def _setup_agent_loggers(self):
        """Setup loggers for individual agents."""
        # Create agents log file
        agents_log_file = self.log_dir / "agents.log"
        
        # Rotating file handler for agents
        agents_handler = logging.handlers.RotatingFileHandler(
            agents_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        agents_handler.setLevel(self.log_level)
        agents_handler.setFormatter(self.detailed_formatter)
        
        # Setup logger for all bmad agents
        agents_logger = logging.getLogger("bmad")
        agents_logger.setLevel(self.log_level)
        agents_logger.addHandler(agents_handler)
        agents_logger.propagate = False  # Don't propagate to root logger
        
        # Add console handler for agents as well
        agents_console = logging.StreamHandler(sys.stdout)
        agents_console.setLevel(logging.WARNING)  # Only warnings and errors to console
        agents_console.setFormatter(self.simple_formatter)
        agents_logger.addHandler(agents_console)
    
    def _setup_workflow_logger(self):
        """Setup logger for workflow operations."""
        workflow_log_file = self.log_dir / "workflows.log"
        
        workflow_handler = logging.handlers.RotatingFileHandler(
            workflow_log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3
        )
        workflow_handler.setLevel(self.log_level)
        workflow_handler.setFormatter(self.detailed_formatter)
        
        workflow_logger = logging.getLogger("bmad.workflow")
        workflow_logger.setLevel(self.log_level)
        workflow_logger.addHandler(workflow_handler)
        workflow_logger.propagate = False
    
    def _setup_system_logger(self):
        """Setup logger for system-level operations."""
        system_log_file = self.log_dir / "system.log"
        
        system_handler = logging.handlers.RotatingFileHandler(
            system_log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3
        )
        system_handler.setLevel(self.log_level)
        system_handler.setFormatter(self.detailed_formatter)
        
        # System logger for config, state management, etc.
        system_logger = logging.getLogger("bmad.system")
        system_logger.setLevel(self.log_level)
        system_logger.addHandler(system_handler)
        system_logger.propagate = False
    
    def get_agent_logger(self, agent_role: str) -> logging.Logger:
        """Get a logger for a specific agent."""
        return logging.getLogger(f"bmad.{agent_role}")
    
    def get_workflow_logger(self) -> logging.Logger:
        """Get the workflow logger."""
        return logging.getLogger("bmad.workflow")
    
    def get_system_logger(self, component: str) -> logging.Logger:
        """Get a system logger for a specific component."""
        return logging.getLogger(f"bmad.system.{component}")
    
    def setup_audit_logging(self):
        """Setup audit logging for important operations."""
        audit_log_file = self.log_dir / "audit.log"
        
        audit_handler = logging.handlers.RotatingFileHandler(
            audit_log_file,
            maxBytes=20*1024*1024,  # 20MB
            backupCount=10
        )
        audit_handler.setLevel(logging.INFO)
        audit_handler.setFormatter(self.json_formatter)
        
        audit_logger = logging.getLogger("bmad.audit")
        audit_logger.setLevel(logging.INFO)
        audit_logger.addHandler(audit_handler)
        audit_logger.propagate = False
        
        return audit_logger
    
    def log_agent_interaction(self, from_agent: str, to_agent: str, 
                            message_type: str, success: bool, 
                            details: Optional[Dict[str, Any]] = None):
        """Log agent-to-agent interactions for audit purposes."""
        audit_logger = logging.getLogger("bmad.audit")
        
        audit_data = {
            'timestamp': datetime.now().isoformat(),
            'event_type': 'agent_interaction',
            'from_agent': from_agent,
            'to_agent': to_agent,
            'message_type': message_type,
            'success': success,
            'details': details or {}
        }
        
        audit_logger.info(f"Agent interaction: {audit_data}")
    
    def log_workflow_event(self, workflow_id: str, event_type: str, 
                          step: Optional[str] = None, 
                          details: Optional[Dict[str, Any]] = None):
        """Log workflow events for audit purposes."""
        audit_logger = logging.getLogger("bmad.audit")
        
        audit_data = {
            'timestamp': datetime.now().isoformat(),
            'event_type': 'workflow_event',
            'workflow_id': workflow_id,
            'workflow_event': event_type,
            'step': step,
            'details': details or {}
        }
        
        audit_logger.info(f"Workflow event: {audit_data}")
    
    def set_log_level(self, level: str):
        """Dynamically change log level for all BMad loggers."""
        new_level = getattr(logging, level.upper(), logging.INFO)
        
        # Update all bmad loggers
        for logger_name in ['bmad', 'bmad.workflow', 'bmad.system', 'bmad.audit']:
            logger = logging.getLogger(logger_name)
            logger.setLevel(new_level)
            
            # Update handlers
            for handler in logger.handlers:
                handler.setLevel(new_level)
        
        self.log_level = new_level

class JsonFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""
    
    def format(self, record):
        import json
        
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        if hasattr(record, 'extra_data'):
            log_data.update(record.extra_data)
        
        return json.dumps(log_data)

def setup_logging(log_dir: str = "logs", log_level: str = "INFO") -> BMadLogger:
    """Setup logging for the BMad agents system."""
    bmad_logger = BMadLogger(log_dir, log_level)
    bmad_logger.setup_audit_logging()
    return bmad_logger

def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name."""
    return logging.getLogger(name)