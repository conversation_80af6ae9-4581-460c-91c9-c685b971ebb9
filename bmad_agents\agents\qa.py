from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from ..base.bmad_agent import BMadAgent

class TestPlan(BaseModel):
    plan_id: str
    user_story_id: str
    test_objectives: List[str]
    test_scope: str
    test_approach: str
    test_types: List[str]  # unit, integration, system, acceptance, performance, security
    entry_criteria: List[str]
    exit_criteria: List[str]
    test_environment: str
    test_data_requirements: List[str]
    risk_assessment: List[str]
    estimated_effort: float  # hours

class TestScenario(BaseModel):
    scenario_id: str
    title: str
    description: str
    test_type: str
    priority: str  # low, medium, high, critical
    preconditions: List[str]
    test_steps: List[str]
    expected_results: List[str]
    test_data: Optional[Dict[str, Any]] = None
    automation_feasible: bool = False
    estimated_execution_time: float  # minutes

class TestExecution(BaseModel):
    execution_id: str
    scenario_id: str
    executed_by: str
    execution_date: datetime = Field(default_factory=datetime.now)
    test_environment: str
    actual_results: List[str]
    status: str  # passed, failed, blocked, skipped
    defects_found: List[str] = Field(default_factory=list)
    execution_notes: str
    screenshots: List[str] = Field(default_factory=list)
    execution_time: float  # minutes

class DefectReport(BaseModel):
    defect_id: str
    title: str
    description: str
    severity: str  # low, medium, high, critical
    priority: str  # low, medium, high, critical
    test_scenario_id: Optional[str] = None
    steps_to_reproduce: List[str]
    expected_behavior: str
    actual_behavior: str
    environment: str
    browser_version: Optional[str] = None
    attachments: List[str] = Field(default_factory=list)
    status: str = "open"  # open, in_progress, resolved, closed, rejected
    assigned_to: Optional[str] = None
    found_date: datetime = Field(default_factory=datetime.now)

class QualityMetrics(BaseModel):
    test_coverage_percentage: float
    defect_density: float  # defects per KLOC
    test_execution_rate: float  # percentage
    pass_rate: float  # percentage
    defect_escape_rate: float  # percentage
    automation_coverage: float  # percentage
    mean_time_to_detect: float  # hours
    mean_time_to_resolve: float  # hours
    customer_satisfaction_score: Optional[float] = None

class RiskAssessment(BaseModel):
    risk_id: str
    risk_category: str  # functional, performance, security, usability, compatibility
    description: str
    probability: str  # low, medium, high
    impact: str  # low, medium, high
    risk_level: str  # low, medium, high, critical
    mitigation_strategy: str
    contingency_plan: str
    owner: str
    status: str = "identified"  # identified, mitigated, accepted, transferred

class TestAutomation(BaseModel):
    automation_id: str
    test_scenarios: List[str]  # scenario IDs
    framework: str
    test_scripts: List[str]  # file paths or script names
    execution_schedule: str
    maintenance_effort: str  # low, medium, high
    roi_analysis: str
    success_criteria: List[str]
    implementation_timeline: str

class PerformanceTest(BaseModel):
    test_id: str
    test_name: str
    test_type: str  # load, stress, volume, spike, endurance
    objectives: List[str]
    user_load: int
    duration: int  # minutes
    success_criteria: Dict[str, float]  # response_time, throughput, etc.
    test_data: str
    environment_config: str
    monitoring_points: List[str]

class SecurityTest(BaseModel):
    test_id: str
    test_name: str
    security_category: str  # authentication, authorization, data_protection, etc.
    vulnerability_type: str
    test_approach: str
    tools_used: List[str]
    test_scenarios: List[str]
    compliance_standards: List[str]  # OWASP, GDPR, etc.
    risk_level: str

class QualityReport(BaseModel):
    report_id: str
    reporting_period: str
    test_summary: Dict[str, int]  # total, passed, failed, blocked
    quality_metrics: QualityMetrics
    defect_summary: Dict[str, int]  # by severity, status, etc.
    risk_status: List[RiskAssessment]
    recommendations: List[str]
    next_steps: List[str]
    generated_date: datetime = Field(default_factory=datetime.now)

class QAAgent(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are an expert Quality Assurance Engineer in the BMad Method framework.
        
        Your responsibilities include:
        - Creating comprehensive test plans and strategies
        - Designing and executing test scenarios
        - Identifying and reporting defects
        - Performing various types of testing (functional, performance, security)
        - Implementing test automation strategies
        - Conducting risk assessments
        - Measuring and reporting quality metrics
        - Ensuring compliance with quality standards
        
        Always focus on thorough testing coverage and quality assurance.
        Think from the user's perspective and consider edge cases.
        Prioritize critical functionality and high-risk areas.
        """
        
        super().__init__(
            role="qa",
            system_prompt=system_prompt,
            response_model=TestPlan
        )
    
    async def create_test_plan(self, user_story: str, acceptance_criteria: List[str], technical_specs: str) -> TestPlan:
        """Create comprehensive test plan for a user story."""
        prompt = f"""
        Create a comprehensive test plan for the following user story:
        
        User Story:
        {user_story}
        
        Acceptance Criteria:
        {chr(10).join(acceptance_criteria)}
        
        Technical Specifications:
        {technical_specs}
        
        Create a test plan including:
        - Clear test objectives aligned with acceptance criteria
        - Comprehensive test scope and boundaries
        - Appropriate test approach and strategy
        - Multiple test types (functional, non-functional, etc.)
        - Clear entry and exit criteria
        - Test environment requirements
        - Test data needs and constraints
        - Risk assessment and mitigation
        - Realistic effort estimation
        
        Ensure the plan covers all critical functionality and edge cases.
        """
        
        return await self.execute_with_logging(prompt)
    
    async def design_test_scenarios(self, test_plan: TestPlan, requirements: List[str]) -> List[TestScenario]:
        """Design detailed test scenarios based on test plan."""
        prompt = f"""
        Design detailed test scenarios based on the following test plan:
        
        Test Objectives: {', '.join(test_plan.test_objectives)}
        Test Scope: {test_plan.test_scope}
        Test Types: {', '.join(test_plan.test_types)}
        
        Requirements:
        {chr(10).join(requirements)}
        
        Create test scenarios covering:
        - Positive test cases (happy path)
        - Negative test cases (error conditions)
        - Boundary value testing
        - Edge cases and corner cases
        - Integration scenarios
        - User workflow testing
        - Data validation testing
        - Security testing scenarios
        
        For each scenario, provide:
        - Clear, descriptive title
        - Detailed test steps
        - Expected results
        - Appropriate priority level
        - Preconditions and test data
        - Automation feasibility assessment
        """
        
        class TestScenarioList(BaseModel):
            scenarios: List[TestScenario]
        
        temp_agent = BMadAgent(
            role="qa-scenarios",
            system_prompt=self.system_prompt,
            response_model=TestScenarioList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.scenarios
    
    async def execute_test_scenarios(self, scenarios: List[TestScenario], test_environment: str) -> List[TestExecution]:
        """Execute test scenarios and record results."""
        scenarios_summary = "\n".join([f"- {scenario.title}: {len(scenario.test_steps)} steps" for scenario in scenarios])
        
        prompt = f"""
        Execute the following test scenarios in the specified environment:
        
        Test Scenarios:
        {scenarios_summary}
        
        Test Environment: {test_environment}
        
        For each scenario execution, provide:
        - Detailed actual results for each step
        - Clear pass/fail status with justification
        - Any defects or issues discovered
        - Execution notes and observations
        - Actual execution time
        - Screenshots or evidence where applicable
        
        Consider:
        - Environment-specific behaviors
        - Performance observations
        - Usability issues
        - Integration problems
        - Data integrity concerns
        """
        
        class TestExecutionList(BaseModel):
            executions: List[TestExecution]
        
        temp_agent = BMadAgent(
            role="qa-execution",
            system_prompt=self.system_prompt,
            response_model=TestExecutionList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.executions
    
    async def analyze_defects(self, test_executions: List[TestExecution]) -> List[DefectReport]:
        """Analyze test executions and create defect reports."""
        failed_executions = [exec for exec in test_executions if exec.status == "failed"]
        executions_summary = "\n".join([f"Scenario: {exec.scenario_id}\nIssues: {', '.join(exec.defects_found)}" for exec in failed_executions])
        
        prompt = f"""
        Analyze the following failed test executions and create detailed defect reports:
        
        Failed Executions:
        {executions_summary}
        
        For each defect, create a comprehensive report including:
        - Clear, descriptive title
        - Detailed description of the issue
        - Appropriate severity and priority classification
        - Step-by-step reproduction instructions
        - Expected vs actual behavior
        - Environment and configuration details
        - Impact assessment on users and system
        - Suggested workarounds if available
        
        Classify severity based on:
        - Critical: System crashes, data loss, security vulnerabilities
        - High: Major functionality broken, significant user impact
        - Medium: Minor functionality issues, workarounds available
        - Low: Cosmetic issues, minor inconveniences
        """
        
        class DefectReportList(BaseModel):
            defects: List[DefectReport]
        
        temp_agent = BMadAgent(
            role="qa-defects",
            system_prompt=self.system_prompt,
            response_model=DefectReportList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.defects
    
    async def assess_quality_risks(self, project_context: str, technical_architecture: str) -> List[RiskAssessment]:
        """Assess quality risks for the project."""
        prompt = f"""
        Assess quality risks based on the project context and technical architecture:
        
        Project Context:
        {project_context}
        
        Technical Architecture:
        {technical_architecture}
        
        Identify and assess risks in categories:
        - Functional risks (feature completeness, correctness)
        - Performance risks (scalability, response time)
        - Security risks (vulnerabilities, data protection)
        - Usability risks (user experience, accessibility)
        - Compatibility risks (browser, device, integration)
        - Operational risks (deployment, monitoring, maintenance)
        
        For each risk, provide:
        - Clear description and context
        - Probability and impact assessment
        - Overall risk level calculation
        - Specific mitigation strategies
        - Contingency plans for high-risk items
        - Risk owner assignment
        """
        
        class RiskAssessmentList(BaseModel):
            risks: List[RiskAssessment]
        
        temp_agent = BMadAgent(
            role="qa-risks",
            system_prompt=self.system_prompt,
            response_model=RiskAssessmentList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.risks
    
    async def design_performance_tests(self, performance_requirements: Dict[str, Any], system_architecture: str) -> List[PerformanceTest]:
        """Design performance test scenarios."""
        prompt = f"""
        Design comprehensive performance tests based on requirements and architecture:
        
        Performance Requirements:
        {performance_requirements}
        
        System Architecture:
        {system_architecture}
        
        Create performance tests covering:
        - Load testing (normal expected load)
        - Stress testing (beyond normal capacity)
        - Volume testing (large amounts of data)
        - Spike testing (sudden load increases)
        - Endurance testing (sustained load over time)
        
        For each test, define:
        - Clear objectives and success criteria
        - Realistic user load patterns
        - Test duration and ramp-up strategy
        - Key performance indicators to monitor
        - Test data requirements
        - Environment configuration needs
        - Monitoring and measurement points
        """
        
        class PerformanceTestList(BaseModel):
            performance_tests: List[PerformanceTest]
        
        temp_agent = BMadAgent(
            role="qa-performance",
            system_prompt=self.system_prompt,
            response_model=PerformanceTestList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.performance_tests
    
    async def design_security_tests(self, security_requirements: List[str], application_type: str) -> List[SecurityTest]:
        """Design security test scenarios."""
        prompt = f"""
        Design comprehensive security tests based on requirements and application type:
        
        Security Requirements:
        {chr(10).join(security_requirements)}
        
        Application Type: {application_type}
        
        Create security tests covering:
        - Authentication and authorization testing
        - Input validation and injection attacks
        - Session management security
        - Data protection and encryption
        - Access control mechanisms
        - Error handling and information disclosure
        - Configuration and deployment security
        
        Follow security testing standards:
        - OWASP Top 10 vulnerabilities
        - Industry-specific compliance requirements
        - Common attack vectors and scenarios
        - Security best practices validation
        
        For each test, provide:
        - Specific vulnerability being tested
        - Test approach and methodology
        - Tools and techniques to be used
        - Expected security controls
        - Risk assessment if vulnerabilities found
        """
        
        class SecurityTestList(BaseModel):
            security_tests: List[SecurityTest]
        
        temp_agent = BMadAgent(
            role="qa-security",
            system_prompt=self.system_prompt,
            response_model=SecurityTestList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.security_tests
    
    async def create_automation_strategy(self, test_scenarios: List[TestScenario], constraints: Dict[str, Any]) -> TestAutomation:
        """Create test automation strategy and plan."""
        automatable_scenarios = [scenario for scenario in test_scenarios if scenario.automation_feasible]
        scenarios_summary = "\n".join([f"- {scenario.title}: {scenario.test_type}" for scenario in automatable_scenarios])
        
        prompt = f"""
        Create a test automation strategy for the following scenarios:
        
        Automatable Scenarios:
        {scenarios_summary}
        
        Constraints:
        {constraints}
        
        Develop automation strategy including:
        - Appropriate automation framework selection
        - Test script organization and structure
        - Execution scheduling and triggers
        - Maintenance and update procedures
        - ROI analysis and justification
        - Success criteria and metrics
        - Implementation timeline and phases
        - Resource requirements and skills needed
        
        Consider:
        - Test stability and reliability
        - Maintenance overhead
        - Execution speed and efficiency
        - Integration with CI/CD pipeline
        - Reporting and result analysis
        """
        
        temp_agent = BMadAgent(
            role="qa-automation",
            system_prompt=self.system_prompt,
            response_model=TestAutomation
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def generate_quality_report(self, test_executions: List[TestExecution], defects: List[DefectReport], metrics_data: Dict[str, Any]) -> QualityReport:
        """Generate comprehensive quality report."""
        execution_summary = f"Total: {len(test_executions)}, Passed: {len([e for e in test_executions if e.status == 'passed'])}"
        defect_summary = f"Total: {len(defects)}, Critical: {len([d for d in defects if d.severity == 'critical'])}"
        
        prompt = f"""
        Generate a comprehensive quality report based on testing results:
        
        Test Execution Summary:
        {execution_summary}
        
        Defect Summary:
        {defect_summary}
        
        Metrics Data:
        {metrics_data}
        
        Create a quality report including:
        - Executive summary of testing activities
        - Test execution statistics and trends
        - Quality metrics and KPIs
        - Defect analysis and categorization
        - Risk status and mitigation progress
        - Quality trends and improvements
        - Recommendations for next steps
        - Action items and priorities
        
        Focus on:
        - Clear, actionable insights
        - Data-driven recommendations
        - Risk-based prioritization
        - Continuous improvement opportunities
        """
        
        temp_agent = BMadAgent(
            role="qa-reporting",
            system_prompt=self.system_prompt,
            response_model=QualityReport
        )
        
        return await temp_agent.execute_with_logging(prompt)