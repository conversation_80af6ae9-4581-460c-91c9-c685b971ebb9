from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from ..base.models import WorkflowState, WorkflowStep
from ..base.state_manager import StateManager
import uuid
from datetime import datetime

class BaseWorkflow(ABC):
    """Base class for all BMad workflows."""
    
    def __init__(self, workflow_type: str):
        self.workflow_type = workflow_type
        self.state_manager = StateManager()
        self.steps = self._define_steps()
    
    @abstractmethod
    def _define_steps(self) -> List[WorkflowStep]:
        """Define the workflow steps."""
        pass
    
    async def start_workflow(self, context: Dict[str, Any]) -> WorkflowState:
        """Start a new workflow instance."""
        workflow_id = str(uuid.uuid4())
        
        state = WorkflowState(
            workflow_id=workflow_id,
            workflow_type=self.workflow_type,
            current_step=self.steps[0].step_id,
            status="active",
            progress=0.0,
            started_at=datetime.now(),
            context=context,
            participants=[],
            completed_steps=[],
            pending_steps=[step.step_id for step in self.steps[1:]]
        )
        
        self.state_manager.save_workflow_state(state)
        return state
    
    async def execute_step(self, workflow_id: str, step_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the current workflow step."""
        state = self.state_manager.load_workflow_state(workflow_id)
        if not state:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        current_step = self._get_step(state.current_step)
        if not current_step:
            raise ValueError(f"Step {state.current_step} not found")
        
        # Execute step logic
        result = await self._execute_step_logic(current_step, step_input, state)
        
        # Update state
        state.completed_steps.append(state.current_step)
        
        # Remove current step from pending steps if it exists
        if state.current_step in state.pending_steps:
            state.pending_steps.remove(state.current_step)
            
        # Update progress
        state.progress = len(state.completed_steps) / len(self.steps)
        
        next_step = self._get_next_step(state.current_step)
        
        if next_step:
            state.current_step = next_step.step_id
        else:
            state.current_step = None  # Workflow completed
            state.status = "completed"
            state.progress = 1.0
            
        # Save updated state
        self.state_manager.save_workflow_state(state)
        
        state.shared_context.update(result.get('context_updates', {}))
        await self.state_manager.save_state(workflow_id, state)
        
        return result
    
    @abstractmethod
    async def _execute_step_logic(self, step: WorkflowStep, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute the logic for a specific step."""
        pass
    
    def _get_step(self, step_id: str) -> Optional[WorkflowStep]:
        """Get step by ID."""
        return next((step for step in self.steps if step.step_id == step_id), None)
    
    def _get_next_step(self, current_step_id: str) -> Optional[WorkflowStep]:
        """Get the next step in the workflow."""
        current_index = next((i for i, step in enumerate(self.steps) if step.step_id == current_step_id), -1)
        if current_index >= 0 and current_index < len(self.steps) - 1:
            return self.steps[current_index + 1]
        return None
    
    async def get_workflow_status(self, workflow_id: str) -> Optional[WorkflowState]:
        """Get the current status of a workflow."""
        return await self.state_manager.load_state(workflow_id)
    
    async def list_active_workflows(self) -> List[str]:
        """List all active workflow IDs."""
        return await self.state_manager.list_workflows()
    
    def get_workflow_steps(self) -> List[WorkflowStep]:
        """Get all workflow steps."""
        return self.steps
    
    def get_step_dependencies(self, step_id: str) -> List[str]:
        """Get dependencies for a specific step."""
        step = self._get_step(step_id)
        return step.dependencies if step else []