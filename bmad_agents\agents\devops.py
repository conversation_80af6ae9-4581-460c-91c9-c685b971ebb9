from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from ..base.bmad_agent import BMadAgent

class InfrastructureComponent(BaseModel):
    component_id: str
    name: str
    type: str  # server, database, load_balancer, cdn, etc.
    specifications: Dict[str, Any]
    dependencies: List[str] = Field(default_factory=list)
    configuration: Dict[str, Any] = Field(default_factory=dict)
    monitoring_endpoints: List[str] = Field(default_factory=list)
    backup_strategy: Optional[str] = None
    scaling_policy: Optional[str] = None

class DeploymentPipeline(BaseModel):
    pipeline_id: str
    name: str
    stages: List[str]  # build, test, staging, production
    triggers: List[str]  # manual, git_push, scheduled
    build_steps: List[str]
    test_steps: List[str]
    deployment_steps: List[str]
    rollback_strategy: str
    approval_gates: List[str] = Field(default_factory=list)
    notifications: List[str] = Field(default_factory=list)
    estimated_duration: int  # minutes

class EnvironmentConfig(BaseModel):
    environment_name: str  # development, staging, production
    infrastructure_components: List[InfrastructureComponent]
    configuration_variables: Dict[str, str]
    secrets_management: Dict[str, str]
    networking_config: Dict[str, Any]
    security_policies: List[str]
    monitoring_config: Dict[str, Any]
    backup_policies: List[str]
    access_controls: Dict[str, List[str]]

class DeploymentPlan(BaseModel):
    plan_id: str
    release_version: str
    target_environment: str
    deployment_strategy: str  # blue_green, rolling, canary
    pre_deployment_checks: List[str]
    deployment_steps: List[str]
    post_deployment_validation: List[str]
    rollback_plan: List[str]
    estimated_downtime: int  # minutes
    risk_assessment: List[str]
    communication_plan: str

class MonitoringAlert(BaseModel):
    alert_id: str
    alert_name: str
    severity: str  # low, medium, high, critical
    metric: str
    threshold: float
    condition: str  # greater_than, less_than, equals
    duration: int  # minutes
    notification_channels: List[str]
    escalation_policy: str
    runbook_url: Optional[str] = None

class SecurityScan(BaseModel):
    scan_id: str
    scan_type: str  # vulnerability, compliance, penetration
    target: str  # application, infrastructure, network
    scan_date: datetime = Field(default_factory=datetime.now)
    findings: List[str]
    severity_breakdown: Dict[str, int]
    compliance_status: str
    remediation_recommendations: List[str]
    next_scan_date: datetime

class BackupStrategy(BaseModel):
    strategy_id: str
    backup_type: str  # full, incremental, differential
    frequency: str  # daily, weekly, monthly
    retention_policy: str
    storage_location: str
    encryption_enabled: bool
    compression_enabled: bool
    verification_schedule: str
    recovery_time_objective: int  # minutes
    recovery_point_objective: int  # minutes

class PerformanceMetrics(BaseModel):
    metric_name: str
    current_value: float
    target_value: float
    unit: str
    trend: str  # improving, stable, degrading
    measurement_period: str
    threshold_breaches: int
    recommendations: List[str]

class IncidentResponse(BaseModel):
    incident_id: str
    title: str
    severity: str  # low, medium, high, critical
    status: str  # open, investigating, resolved, closed
    affected_services: List[str]
    impact_description: str
    root_cause: Optional[str] = None
    resolution_steps: List[str]
    lessons_learned: List[str]
    prevention_measures: List[str]
    incident_start: datetime
    incident_end: Optional[datetime] = None

class ScalingPolicy(BaseModel):
    policy_id: str
    resource_type: str  # cpu, memory, requests_per_second
    scale_up_threshold: float
    scale_down_threshold: float
    min_instances: int
    max_instances: int
    cooldown_period: int  # minutes
    scaling_increment: int
    target_utilization: float

class DevOpsAgent(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are an expert DevOps Engineer in the BMad Method framework.
        
        Your responsibilities include:
        - Designing and managing infrastructure architecture
        - Creating and maintaining CI/CD pipelines
        - Implementing deployment strategies and automation
        - Monitoring system performance and reliability
        - Managing security and compliance requirements
        - Implementing backup and disaster recovery strategies
        - Optimizing system performance and scalability
        - Incident response and troubleshooting
        
        Always focus on reliability, security, and automation.
        Consider scalability, cost optimization, and operational efficiency.
        Implement infrastructure as code and best practices.
        """
        
        super().__init__(
            role="devops",
            system_prompt=system_prompt,
            response_model=DeploymentPlan
        )
    
    async def design_infrastructure(self, requirements: Dict[str, Any], constraints: Dict[str, Any]) -> List[InfrastructureComponent]:
        """Design infrastructure architecture based on requirements."""
        prompt = f"""
        Design infrastructure architecture based on the following requirements and constraints:
        
        Requirements:
        {requirements}
        
        Constraints:
        {constraints}
        
        Design infrastructure including:
        - Compute resources (servers, containers, serverless)
        - Storage solutions (databases, file systems, object storage)
        - Networking components (load balancers, CDN, VPN)
        - Security components (firewalls, WAF, identity management)
        - Monitoring and logging infrastructure
        - Backup and disaster recovery systems
        
        For each component, specify:
        - Detailed specifications and sizing
        - Dependencies and relationships
        - Configuration requirements
        - Monitoring and health check endpoints
        - Backup and scaling strategies
        
        Consider:
        - High availability and fault tolerance
        - Scalability and performance requirements
        - Security and compliance needs
        - Cost optimization opportunities
        - Operational complexity and maintenance
        """
        
        class InfrastructureDesign(BaseModel):
            components: List[InfrastructureComponent]
        
        temp_agent = BMadAgent(
            role="devops-infrastructure",
            system_prompt=self.system_prompt,
            response_model=InfrastructureDesign
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.components
    
    async def create_deployment_pipeline(self, application_type: str, environments: List[str], requirements: Dict[str, Any]) -> DeploymentPipeline:
        """Create CI/CD deployment pipeline."""
        prompt = f"""
        Create a comprehensive CI/CD deployment pipeline for the following:
        
        Application Type: {application_type}
        Target Environments: {', '.join(environments)}
        
        Requirements:
        {requirements}
        
        Design pipeline including:
        - Build stage with compilation, packaging, and artifact creation
        - Test stage with unit, integration, and quality gate tests
        - Security scanning and vulnerability assessment
        - Deployment stages for each environment
        - Approval gates and manual checkpoints
        - Automated rollback mechanisms
        - Notification and reporting systems
        
        Consider:
        - Pipeline efficiency and speed
        - Quality gates and testing coverage
        - Security and compliance checks
        - Deployment strategies (blue-green, canary, rolling)
        - Monitoring and observability integration
        - Failure handling and recovery
        """
        
        temp_agent = BMadAgent(
            role="devops-pipeline",
            system_prompt=self.system_prompt,
            response_model=DeploymentPipeline
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def configure_environments(self, infrastructure: List[InfrastructureComponent], environment_types: List[str]) -> List[EnvironmentConfig]:
        """Configure different environments (dev, staging, prod)."""
        infra_summary = "\n".join([f"- {comp.name}: {comp.type}" for comp in infrastructure])
        
        prompt = f"""
        Configure environments based on the infrastructure design:
        
        Infrastructure Components:
        {infra_summary}
        
        Environment Types: {', '.join(environment_types)}
        
        For each environment, configure:
        - Appropriate sizing and resource allocation
        - Environment-specific configuration variables
        - Secrets management and security policies
        - Networking configuration and access controls
        - Monitoring and alerting setup
        - Backup policies and retention schedules
        - Access controls and permissions
        
        Environment-specific considerations:
        - Development: Fast iteration, debugging capabilities
        - Staging: Production-like, testing and validation
        - Production: High availability, performance, security
        
        Ensure:
        - Consistent configuration management
        - Proper isolation between environments
        - Cost optimization for non-production environments
        - Security best practices across all environments
        """
        
        class EnvironmentConfigList(BaseModel):
            environments: List[EnvironmentConfig]
        
        temp_agent = BMadAgent(
            role="devops-environments",
            system_prompt=self.system_prompt,
            response_model=EnvironmentConfigList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.environments
    
    async def create_deployment_plan(self, release_info: Dict[str, Any], target_environment: str) -> DeploymentPlan:
        """Create detailed deployment plan for a release."""
        prompt = f"""
        Create a detailed deployment plan for the following release:
        
        Release Information:
        {release_info}
        
        Target Environment: {target_environment}
        
        Create deployment plan including:
        - Pre-deployment checks and validations
        - Step-by-step deployment procedures
        - Post-deployment validation and testing
        - Comprehensive rollback plan and procedures
        - Risk assessment and mitigation strategies
        - Communication plan and stakeholder notifications
        - Estimated downtime and maintenance windows
        
        Consider:
        - Deployment strategy (blue-green, canary, rolling)
        - Database migrations and schema changes
        - Configuration updates and feature flags
        - Third-party service dependencies
        - Monitoring and alerting during deployment
        - User communication and impact minimization
        """
        
        return await self.execute_with_logging(prompt)
    
    async def setup_monitoring(self, infrastructure: List[InfrastructureComponent], sla_requirements: Dict[str, Any]) -> List[MonitoringAlert]:
        """Setup comprehensive monitoring and alerting."""
        services = [comp.name for comp in infrastructure]
        
        prompt = f"""
        Setup comprehensive monitoring and alerting for the following services:
        
        Services: {', '.join(services)}
        
        SLA Requirements:
        {sla_requirements}
        
        Create monitoring alerts for:
        - System health and availability metrics
        - Performance metrics (response time, throughput)
        - Resource utilization (CPU, memory, disk, network)
        - Application-specific metrics and KPIs
        - Security events and anomalies
        - Business metrics and user experience
        - Infrastructure events and changes
        
        For each alert, define:
        - Appropriate thresholds based on SLA requirements
        - Severity levels and escalation policies
        - Notification channels and recipients
        - Runbook references for incident response
        - Alert fatigue prevention measures
        
        Consider:
        - Proactive vs reactive monitoring
        - Alert correlation and noise reduction
        - Dashboard and visualization needs
        - Compliance and audit requirements
        """
        
        class MonitoringSetup(BaseModel):
            alerts: List[MonitoringAlert]
        
        temp_agent = BMadAgent(
            role="devops-monitoring",
            system_prompt=self.system_prompt,
            response_model=MonitoringSetup
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.alerts
    
    async def conduct_security_assessment(self, infrastructure: List[InfrastructureComponent], compliance_requirements: List[str]) -> List[SecurityScan]:
        """Conduct comprehensive security assessment."""
        targets = [f"{comp.name} ({comp.type})" for comp in infrastructure]
        
        prompt = f"""
        Conduct comprehensive security assessment for the following targets:
        
        Infrastructure Targets:
        {chr(10).join(targets)}
        
        Compliance Requirements:
        {', '.join(compliance_requirements)}
        
        Perform security scans including:
        - Vulnerability scanning for known CVEs
        - Configuration security assessment
        - Network security and penetration testing
        - Application security testing (SAST/DAST)
        - Compliance validation and audit
        - Access control and privilege review
        - Data protection and encryption validation
        
        For each scan, provide:
        - Detailed findings and risk assessment
        - Severity classification and impact analysis
        - Compliance status against requirements
        - Specific remediation recommendations
        - Timeline for addressing critical issues
        
        Focus on:
        - OWASP Top 10 vulnerabilities
        - Industry-specific compliance standards
        - Zero-trust security principles
        - Defense in depth strategies
        """
        
        class SecurityAssessment(BaseModel):
            scans: List[SecurityScan]
        
        temp_agent = BMadAgent(
            role="devops-security",
            system_prompt=self.system_prompt,
            response_model=SecurityAssessment
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.scans
    
    async def design_backup_strategy(self, data_requirements: Dict[str, Any], recovery_objectives: Dict[str, int]) -> List[BackupStrategy]:
        """Design comprehensive backup and disaster recovery strategy."""
        prompt = f"""
        Design comprehensive backup and disaster recovery strategy:
        
        Data Requirements:
        {data_requirements}
        
        Recovery Objectives:
        {recovery_objectives}
        
        Create backup strategies covering:
        - Database backups (full, incremental, transaction log)
        - Application data and configuration backups
        - System state and infrastructure backups
        - Cross-region and off-site backup replication
        - Backup verification and integrity testing
        - Automated backup scheduling and monitoring
        
        For each strategy, define:
        - Backup frequency and retention policies
        - Storage locations and redundancy
        - Encryption and security measures
        - Recovery procedures and testing
        - RTO and RPO compliance validation
        
        Consider:
        - Business continuity requirements
        - Compliance and regulatory needs
        - Cost optimization and storage efficiency
        - Disaster recovery scenarios and procedures
        """
        
        class BackupStrategyList(BaseModel):
            strategies: List[BackupStrategy]
        
        temp_agent = BMadAgent(
            role="devops-backup",
            system_prompt=self.system_prompt,
            response_model=BackupStrategyList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.strategies
    
    async def optimize_performance(self, current_metrics: List[PerformanceMetrics], performance_targets: Dict[str, float]) -> Dict[str, Any]:
        """Analyze performance and provide optimization recommendations."""
        metrics_summary = "\n".join([f"- {metric.metric_name}: {metric.current_value} {metric.unit} (target: {metric.target_value})" for metric in current_metrics])
        
        prompt = f"""
        Analyze current performance metrics and provide optimization recommendations:
        
        Current Metrics:
        {metrics_summary}
        
        Performance Targets:
        {performance_targets}
        
        Analyze and provide recommendations for:
        - Infrastructure scaling and resource optimization
        - Application performance tuning
        - Database query optimization
        - Caching strategies and implementation
        - Network optimization and CDN usage
        - Load balancing and traffic distribution
        - Code-level optimizations and bottlenecks
        
        For each recommendation, provide:
        - Specific optimization actions
        - Expected performance improvements
        - Implementation effort and timeline
        - Cost implications and ROI analysis
        - Risk assessment and mitigation
        - Monitoring and validation approach
        """
        
        class PerformanceOptimization(BaseModel):
            optimization_recommendations: List[str]
            priority_actions: List[str]
            expected_improvements: Dict[str, float]
            implementation_timeline: str
            cost_analysis: Dict[str, float]
            risk_assessment: List[str]
        
        temp_agent = BMadAgent(
            role="devops-performance",
            system_prompt=self.system_prompt,
            response_model=PerformanceOptimization
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.model_dump()
    
    async def create_scaling_policies(self, application_metrics: Dict[str, Any], traffic_patterns: Dict[str, Any]) -> List[ScalingPolicy]:
        """Create auto-scaling policies based on metrics and patterns."""
        prompt = f"""
        Create auto-scaling policies based on application metrics and traffic patterns:
        
        Application Metrics:
        {application_metrics}
        
        Traffic Patterns:
        {traffic_patterns}
        
        Create scaling policies for:
        - Horizontal scaling based on CPU/memory utilization
        - Request-based scaling for web applications
        - Queue-based scaling for background processing
        - Predictive scaling based on traffic patterns
        - Cost-optimized scaling with spot instances
        - Multi-metric scaling with composite triggers
        
        For each policy, define:
        - Appropriate scaling triggers and thresholds
        - Minimum and maximum instance limits
        - Scaling increment and cooldown periods
        - Target utilization and performance metrics
        - Cost optimization considerations
        
        Consider:
        - Application startup time and warm-up periods
        - Stateful vs stateless application requirements
        - Database connection limits and constraints
        - Load balancer configuration and health checks
        """
        
        class ScalingPolicyList(BaseModel):
            policies: List[ScalingPolicy]
        
        temp_agent = BMadAgent(
            role="devops-scaling",
            system_prompt=self.system_prompt,
            response_model=ScalingPolicyList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.policies
    
    async def handle_incident(self, incident_details: Dict[str, Any]) -> IncidentResponse:
        """Create incident response plan and procedures."""
        prompt = f"""
        Create incident response plan for the following incident:
        
        Incident Details:
        {incident_details}
        
        Develop incident response including:
        - Immediate containment and stabilization actions
        - Root cause analysis procedures
        - Step-by-step resolution plan
        - Communication plan and stakeholder updates
        - Impact assessment and user communication
        - Post-incident review and lessons learned
        - Prevention measures and improvements
        
        Consider:
        - Incident severity and business impact
        - Available resources and expertise
        - Service level agreements and commitments
        - Regulatory and compliance requirements
        - Customer communication and transparency
        - Documentation and knowledge sharing
        """
        
        temp_agent = BMadAgent(
            role="devops-incident",
            system_prompt=self.system_prompt,
            response_model=IncidentResponse
        )
        
        return await temp_agent.execute_with_logging(prompt)