from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any
from ..base.bmad_agent import BMadAgent

class RequirementAnalysis(BaseModel):
    summary: str = Field(description="High-level summary of requirements")
    functional_requirements: List[str] = Field(description="List of functional requirements")
    non_functional_requirements: List[str] = Field(description="List of non-functional requirements")
    assumptions: List[str] = Field(description="Key assumptions made")
    risks: List[str] = Field(description="Identified risks")
    recommendations: List[str] = Field(description="Recommendations for implementation")

class UserStory(BaseModel):
    title: str
    description: str
    acceptance_criteria: List[str]
    priority: str  # High, Medium, Low
    effort_estimate: str  # Small, Medium, Large
    dependencies: List[str] = Field(default_factory=list)

class UserStoryList(BaseModel):
    stories: List[UserStory]

class AnalystAgent(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are an expert Business Analyst in the BMad Method framework.
        
        Your responsibilities include:
        - Analyzing business requirements and user needs
        - Creating detailed user stories with acceptance criteria
        - Identifying risks and assumptions
        - Providing recommendations for implementation approach
        
        Always provide structured, actionable analysis that can guide development decisions.
        Focus on clarity, completeness, and practical implementation considerations.
        """
        
        super().__init__(
            role="analyst",
            system_prompt=system_prompt,
            response_model=RequirementAnalysis
        )
    
    async def analyze_requirements(self, requirements: str, context: Dict[str, Any] = None) -> RequirementAnalysis:
        """Analyze business requirements and provide structured analysis."""
        prompt = f"""
        Please analyze the following requirements and provide a comprehensive analysis:
        
        Requirements:
        {requirements}
        
        {f"Additional Context: {context}" if context else ""}
        
        Provide a detailed analysis including functional and non-functional requirements,
        assumptions, risks, and implementation recommendations.
        """
        
        result = await self.run(prompt)
        return result
    
    async def create_user_stories(self, analysis: RequirementAnalysis, additional_context: str = "") -> List[UserStory]:
        """Create user stories based on requirements analysis."""
        prompt = f"""
        Based on the following requirements analysis, create detailed user stories:
        
        Analysis Summary: {analysis.summary}
        Functional Requirements: {', '.join(analysis.functional_requirements)}
        
        {f"Additional Context: {additional_context}" if additional_context else ""}
        
        Create comprehensive user stories with:
        - Clear titles and descriptions
        - Specific acceptance criteria
        - Priority levels (High/Medium/Low)
        - Effort estimates (Small/Medium/Large)
        - Dependencies between stories
        
        Return as a list of UserStory objects.
        """
        
        # Create temporary agent for user story generation
        temp_agent = BMadAgent(
            role="analyst-stories",
            system_prompt=self.system_prompt,
            response_model=UserStoryList
        )
        
        result = await temp_agent.run(prompt)
        return result.stories
    
    async def prioritize_stories(self, stories: List[UserStory], criteria: Dict[str, Any] = None) -> List[UserStory]:
        """Prioritize user stories based on business value and criteria."""
        stories_text = "\n".join([f"- {story.title}: {story.description}" for story in stories])
        
        prompt = f"""
        Prioritize the following user stories based on business value, dependencies, and implementation complexity:
        
        Stories:
        {stories_text}
        
        {f"Prioritization Criteria: {criteria}" if criteria else ""}
        
        Return the stories in prioritized order with updated priority levels.
        """
        
        temp_agent = BMadAgent(
            role="analyst-prioritization",
            system_prompt=self.system_prompt,
            response_model=UserStoryList
        )
        
        result = await temp_agent.run(prompt)
        return result.stories