#!/usr/bin/env python3
"""
Basic example demonstrating BMad Agent Infrastructure.
This example shows how to use the base BMad components.
"""

import asyncio
import os
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import BMad components
from bmad_agents import (
    BMadAgent,
    AgentMessage,
    AgentRequest,
    AgentResponse,
    StateManager,
    BMadConfig,
    MessageType,
    Priority,
    initialize_bmad_system
)

async def basic_agent_demo():
    """Demonstrate basic BMad agent functionality."""
    print("🚀 BMad Agent Infrastructure Demo")
    print("=" * 40)
    
    # Initialize the BMad system
    logger_system = initialize_bmad_system(log_level="INFO")
    print("✅ BMad system initialized")
    
    # Create a configuration instance
    config = BMadConfig()
    print(f"✅ Configuration loaded - Default model: {config.get_global_config('default_model')}")
    
    # Create a state manager
    state_manager = StateManager()
    print("✅ State manager initialized")
    
    # Create a simple BMad agent
    analyst_agent = BMadAgent(
        role="demo-analyst",
        model="gemini-2.5-flash",
        system_prompt="You are a demo analyst agent. Provide brief, helpful analysis."
    )
    
    agent_info = analyst_agent.get_agent_info()
    print(f"✅ Agent created: {agent_info['role']}")
    print(f"   Model: {agent_info['model']}")
    print(f"   Created: {agent_info['created_at']}")
    
    # Create and save an agent message
    demo_message = AgentMessage(
        id="demo-msg-001",
        type=MessageType.REQUEST,
        from_agent="user",
        to_agent="demo-analyst",
        priority=Priority.NORMAL,
        content={
            "task": "analyze",
            "data": "This is a demo message to test the BMad infrastructure"
        },
        workflow_id="demo-workflow-001"
    )
    
    # Save the message using state manager
    message_saved = state_manager.save_message(demo_message)
    if message_saved:
        print("✅ Demo message saved to state manager")
    
    # Create a workflow state
    from bmad_agents.base.communication import WorkflowState
    demo_workflow = WorkflowState(
        workflow_id="demo-workflow-001",
        workflow_type="demo",
        current_step="analysis",
        status="active",
        progress=0.3,
        started_at=datetime.now(),
        context={"demo": True, "purpose": "infrastructure_test"},
        participants=["demo-analyst"],
        completed_steps=["initialization"],
        pending_steps=["analysis", "completion"]
    )
    
    # Save workflow state
    workflow_saved = state_manager.save_workflow_state(demo_workflow)
    if workflow_saved:
        print("✅ Demo workflow state saved")
        
        # Load it back to verify
        loaded_workflow = state_manager.load_workflow_state("demo-workflow-001")
        if loaded_workflow:
            print(f"✅ Workflow loaded - Status: {loaded_workflow.status}, Progress: {loaded_workflow.progress}")
    
    # Demonstrate agent request/response structure
    demo_request = AgentRequest(
        id="req-001",
        task_type="analysis",
        parameters={"topic": "BMad Method benefits", "format": "brief"},
        requester="user",
        context={"focus": "benefits", "workflow_step": "analysis"}
    )
    
    print(f"✅ Demo request created: {demo_request.task_type} from {demo_request.requester}")
    
    # Create a mock response
    demo_response = AgentResponse(
        request_id="req-001",
        agent="demo-analyst",
        status="success",
        result={
            "analysis": "BMad Method provides structured approach to software development",
            "benefits": [
                "Clear role definitions",
                "Systematic workflow management", 
                "Enhanced collaboration",
                "Quality assurance integration"
            ]
        },
        metadata={"processing_time": "2.3s", "confidence": 0.95}
    )
    
    print(f"✅ Demo response created: {demo_response.status}")
    
    # Clean up demo data
    cleanup_success = state_manager.delete_workflow_state("demo-workflow-001")
    if cleanup_success:
        print("✅ Demo data cleaned up")
    
    print("\n🎉 BMad Agent Infrastructure Demo Complete!")
    print("\nKey Components Demonstrated:")
    print("  • BMadAgent base class")
    print("  • Communication models (AgentMessage, AgentRequest, AgentResponse)")
    print("  • Workflow state management")
    print("  • Configuration management")
    print("  • Logging infrastructure")
    print("  • State persistence")

def main():
    """Main function."""
    print("BMad Pydantic AI Agents - Basic Infrastructure Example")
    print("=" * 60)
    
    # Run the demo
    asyncio.run(basic_agent_demo())
    
    print("\n✨ Demo completed successfully!")
    print("\nNext Steps:")
    print("  1. Implement specific BMad agents (Analyst, Architect, etc.)")
    print("  2. Create the BMad Orchestrator")
    print("  3. Build workflow engine")
    print("  4. Add integration tests")

if __name__ == "__main__":
    main()