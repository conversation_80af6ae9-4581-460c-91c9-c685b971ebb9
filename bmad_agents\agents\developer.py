from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from ..base.bmad_agent import BMadAgent

class CodeFile(BaseModel):
    file_path: str
    content: str
    language: str
    description: str
    dependencies: List[str] = Field(default_factory=list)
    test_coverage: Optional[float] = None
    complexity_score: Optional[int] = None

class TechnicalTask(BaseModel):
    task_id: str
    title: str
    description: str
    user_story_id: str
    acceptance_criteria: List[str]
    technical_requirements: List[str]
    estimated_hours: float
    complexity: str  # low, medium, high
    dependencies: List[str] = Field(default_factory=list)
    status: str = "todo"  # todo, in_progress, code_review, testing, done
    assigned_developer: Optional[str] = None

class CodeReview(BaseModel):
    review_id: str
    file_path: str
    reviewer: str
    review_date: datetime = Field(default_factory=datetime.now)
    issues_found: List[str]
    suggestions: List[str]
    code_quality_score: int = Field(ge=1, le=10)
    security_concerns: List[str] = Field(default_factory=list)
    performance_notes: List[str] = Field(default_factory=list)
    approval_status: str  # approved, needs_changes, rejected

class TestCase(BaseModel):
    test_id: str
    test_name: str
    test_type: str  # unit, integration, e2e
    description: str
    test_steps: List[str]
    expected_result: str
    actual_result: Optional[str] = None
    status: str = "pending"  # pending, passed, failed, skipped
    execution_time: Optional[float] = None
    coverage_percentage: Optional[float] = None

class TechnicalDebt(BaseModel):
    debt_id: str
    title: str
    description: str
    affected_files: List[str]
    severity: str  # low, medium, high, critical
    estimated_effort: float  # hours
    business_impact: str
    technical_impact: str
    recommended_solution: str
    priority_score: int = Field(ge=1, le=10)

class ImplementationPlan(BaseModel):
    plan_id: str
    user_story_id: str
    technical_tasks: List[TechnicalTask]
    architecture_decisions: List[str]
    technology_stack: Dict[str, str]
    database_changes: List[str] = Field(default_factory=list)
    api_endpoints: List[str] = Field(default_factory=list)
    estimated_total_hours: float
    risk_factors: List[str] = Field(default_factory=list)
    testing_strategy: str

class CodeGeneration(BaseModel):
    generated_files: List[CodeFile]
    implementation_notes: List[str]
    setup_instructions: List[str]
    dependencies_to_install: List[str] = Field(default_factory=list)
    configuration_changes: List[str] = Field(default_factory=list)
    testing_recommendations: List[str] = Field(default_factory=list)

class RefactoringPlan(BaseModel):
    refactoring_id: str
    target_files: List[str]
    refactoring_type: str  # extract_method, rename, move_class, etc.
    description: str
    benefits: List[str]
    risks: List[str]
    estimated_effort: float
    testing_impact: str
    rollback_plan: str

class DeveloperAgent(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are an expert Software Developer in the BMad Method framework.
        
        Your responsibilities include:
        - Breaking down user stories into technical tasks
        - Generating high-quality, maintainable code
        - Conducting thorough code reviews
        - Writing comprehensive tests
        - Identifying and managing technical debt
        - Implementing best practices and design patterns
        - Ensuring code security and performance
        
        Always focus on code quality, maintainability, and following established patterns.
        Write clean, well-documented code with proper error handling.
        Consider scalability, security, and performance in all implementations.
        """
        
        super().__init__(
            role="developer",
            system_prompt=system_prompt,
            response_model=CodeGeneration
        )
    
    async def create_implementation_plan(self, user_story: str, technical_specs: str) -> ImplementationPlan:
        """Create detailed implementation plan for a user story."""
        prompt = f"""
        Create a comprehensive implementation plan for the following user story:
        
        User Story:
        {user_story}
        
        Technical Specifications:
        {technical_specs}
        
        Break down the implementation into:
        - Specific technical tasks with clear descriptions
        - Architecture decisions and rationale
        - Technology stack recommendations
        - Database schema changes if needed
        - API endpoints and interfaces
        - Realistic effort estimates in hours
        - Risk factors and mitigation strategies
        - Testing strategy and approach
        
        Ensure tasks are granular enough for daily development work.
        """
        
        temp_agent = BMadAgent(
            role="developer-planning",
            system_prompt=self.system_prompt,
            response_model=ImplementationPlan
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def generate_code(self, technical_task: TechnicalTask, architecture_context: str) -> CodeGeneration:
        """Generate code implementation for a technical task."""
        prompt = f"""
        Generate code implementation for the following technical task:
        
        Task: {technical_task.title}
        Description: {technical_task.description}
        Requirements: {', '.join(technical_task.technical_requirements)}
        Acceptance Criteria: {', '.join(technical_task.acceptance_criteria)}
        
        Architecture Context:
        {architecture_context}
        
        Generate:
        - Complete, working code files with proper structure
        - Clear comments and documentation
        - Error handling and validation
        - Appropriate design patterns
        - Security considerations
        - Performance optimizations where relevant
        - Setup and configuration instructions
        - Dependencies and installation requirements
        - Testing recommendations
        
        Follow best practices for the target language and framework.
        """
        
        return await self.execute_with_logging(prompt)
    
    async def review_code(self, code_files: List[CodeFile], review_criteria: Dict[str, Any]) -> List[CodeReview]:
        """Conduct comprehensive code review."""
        files_summary = "\n".join([f"File: {file.file_path}\nLanguage: {file.language}\nDescription: {file.description}" for file in code_files])
        
        prompt = f"""
        Conduct a comprehensive code review for the following files:
        
        {files_summary}
        
        Review Criteria:
        {review_criteria}
        
        For each file, evaluate:
        - Code quality and maintainability
        - Adherence to coding standards and best practices
        - Security vulnerabilities and concerns
        - Performance implications
        - Error handling and edge cases
        - Documentation and comments
        - Test coverage and testability
        - Design patterns and architecture alignment
        
        Provide specific, actionable feedback with examples.
        """
        
        class CodeReviewList(BaseModel):
            reviews: List[CodeReview]
        
        temp_agent = BMadAgent(
            role="developer-review",
            system_prompt=self.system_prompt,
            response_model=CodeReviewList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.reviews
    
    async def generate_tests(self, code_files: List[CodeFile], testing_strategy: str) -> List[TestCase]:
        """Generate comprehensive test cases for code files."""
        files_info = "\n".join([f"File: {file.file_path}\nDescription: {file.description}" for file in code_files])
        
        prompt = f"""
        Generate comprehensive test cases for the following code files:
        
        {files_info}
        
        Testing Strategy:
        {testing_strategy}
        
        Create test cases covering:
        - Unit tests for individual functions/methods
        - Integration tests for component interactions
        - Edge cases and error conditions
        - Performance and load testing scenarios
        - Security testing where applicable
        - User acceptance test scenarios
        
        Ensure tests are:
        - Independent and repeatable
        - Clear and well-documented
        - Covering both positive and negative scenarios
        - Following testing best practices
        """
        
        class TestCaseList(BaseModel):
            test_cases: List[TestCase]
        
        temp_agent = BMadAgent(
            role="developer-testing",
            system_prompt=self.system_prompt,
            response_model=TestCaseList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.test_cases
    
    async def identify_technical_debt(self, codebase_analysis: str) -> List[TechnicalDebt]:
        """Identify and prioritize technical debt in the codebase."""
        prompt = f"""
        Analyze the codebase and identify technical debt items:
        
        Codebase Analysis:
        {codebase_analysis}
        
        Identify technical debt including:
        - Code smells and anti-patterns
        - Outdated dependencies and libraries
        - Performance bottlenecks
        - Security vulnerabilities
        - Lack of test coverage
        - Documentation gaps
        - Architecture inconsistencies
        - Duplicated code and logic
        
        For each debt item, provide:
        - Clear description and location
        - Severity assessment
        - Business and technical impact
        - Effort estimation for resolution
        - Recommended solution approach
        - Priority score for addressing
        """
        
        class TechnicalDebtList(BaseModel):
            debt_items: List[TechnicalDebt]
        
        temp_agent = BMadAgent(
            role="developer-debt",
            system_prompt=self.system_prompt,
            response_model=TechnicalDebtList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.debt_items
    
    async def create_refactoring_plan(self, technical_debt: List[TechnicalDebt], constraints: Dict[str, Any]) -> List[RefactoringPlan]:
        """Create refactoring plan to address technical debt."""
        debt_summary = "\n".join([f"- {debt.title}: {debt.severity} severity, {debt.estimated_effort}h" for debt in technical_debt])
        
        prompt = f"""
        Create a refactoring plan to address the following technical debt:
        
        Technical Debt Items:
        {debt_summary}
        
        Constraints:
        {constraints}
        
        Create refactoring plans that:
        - Address high-priority debt items first
        - Minimize risk and disruption
        - Can be executed incrementally
        - Include proper testing strategies
        - Have clear rollback plans
        - Consider team capacity and timeline
        
        For each refactoring:
        - Define clear scope and objectives
        - Identify benefits and risks
        - Estimate effort and timeline
        - Plan testing and validation approach
        - Define success criteria
        """
        
        class RefactoringPlanList(BaseModel):
            refactoring_plans: List[RefactoringPlan]
        
        temp_agent = BMadAgent(
            role="developer-refactoring",
            system_prompt=self.system_prompt,
            response_model=RefactoringPlanList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.refactoring_plans
    
    async def optimize_performance(self, performance_analysis: str, code_files: List[CodeFile]) -> CodeGeneration:
        """Optimize code performance based on analysis."""
        files_info = "\n".join([f"File: {file.file_path}\nComplexity: {file.complexity_score}" for file in code_files])
        
        prompt = f"""
        Optimize code performance based on the following analysis:
        
        Performance Analysis:
        {performance_analysis}
        
        Code Files to Optimize:
        {files_info}
        
        Apply performance optimizations including:
        - Algorithm and data structure improvements
        - Database query optimization
        - Caching strategies
        - Memory usage optimization
        - Concurrent processing where applicable
        - Resource management improvements
        - Network and I/O optimization
        
        Ensure optimizations:
        - Maintain code readability and maintainability
        - Include performance benchmarks
        - Have proper error handling
        - Are thoroughly tested
        - Document performance improvements
        """
        
        return await self.execute_with_logging(prompt)
    
    async def validate_implementation(self, implementation: CodeGeneration, requirements: List[str]) -> Dict[str, Any]:
        """Validate implementation against requirements."""
        files_summary = "\n".join([f"- {file.file_path}: {file.description}" for file in implementation.generated_files])
        
        prompt = f"""
        Validate the following implementation against requirements:
        
        Generated Files:
        {files_summary}
        
        Requirements:
        {chr(10).join(requirements)}
        
        Validate:
        - Functional requirement coverage
        - Non-functional requirement adherence
        - Code quality and standards compliance
        - Security requirement implementation
        - Performance requirement satisfaction
        - Integration and compatibility
        - Error handling and edge cases
        - Documentation completeness
        
        Provide validation results with specific gaps and recommendations.
        """
        
        class ImplementationValidation(BaseModel):
            requirements_met: List[str]
            requirements_missing: List[str]
            quality_score: int = Field(ge=1, le=10)
            security_compliance: bool
            performance_acceptable: bool
            gaps_identified: List[str]
            improvement_recommendations: List[str]
        
        temp_agent = BMadAgent(
            role="developer-validation",
            system_prompt=self.system_prompt,
            response_model=ImplementationValidation
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.model_dump()