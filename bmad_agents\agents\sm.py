from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from ..base.bmad_agent import BMadAgent

class SprintGoal(BaseModel):
    goal_statement: str
    success_criteria: List[str]
    key_deliverables: List[str]
    risks: List[str] = Field(default_factory=list)
    dependencies: List[str] = Field(default_factory=list)

class SprintBacklogItem(BaseModel):
    item_id: str
    title: str
    description: str
    story_points: int = Field(ge=1, le=21)  # Fibonacci sequence
    priority: int
    assignee: Optional[str] = None
    status: str = "todo"  # todo, in_progress, done
    acceptance_criteria: List[str]
    definition_of_done: List[str]
    blockers: List[str] = Field(default_factory=list)

class SprintPlan(BaseModel):
    sprint_number: int
    sprint_goal: SprintGoal
    start_date: date
    end_date: date
    team_capacity: int  # total story points
    committed_items: List[SprintBacklogItem]
    total_story_points: int
    team_members: List[str]
    ceremonies_schedule: Dict[str, str]  # ceremony -> datetime
    success_metrics: List[str]

class DailyStandupReport(BaseModel):
    report_date: date = Field(default_factory=date.today)
    attendees: List[str]
    team_updates: List[Dict[str, str]]  # member -> {yesterday, today, blockers}
    identified_blockers: List[str]
    action_items: List[str]
    sprint_progress: str  # on_track, at_risk, behind
    burndown_status: str
    recommendations: List[str]

class SprintRetrospective(BaseModel):
    sprint_number: int
    retrospective_date: date
    what_went_well: List[str]
    what_could_improve: List[str]
    action_items: List[str]
    team_mood: str  # excellent, good, neutral, concerning, poor
    velocity_analysis: str
    process_improvements: List[str]
    team_feedback: List[str]
    next_sprint_focus: List[str]

class TeamMetrics(BaseModel):
    sprint_number: int
    velocity: float  # story points completed
    burndown_trend: str  # ahead, on_track, behind
    commitment_accuracy: float  # percentage
    defect_rate: float
    team_satisfaction: float = Field(ge=1, le=10)
    collaboration_score: float = Field(ge=1, le=10)
    delivery_predictability: float = Field(ge=1, le=10)
    improvement_areas: List[str]

class ImpedimentLog(BaseModel):
    impediment_id: str
    title: str
    description: str
    impact_level: str  # low, medium, high, critical
    affected_team_members: List[str]
    date_identified: date = Field(default_factory=date.today)
    status: str = "open"  # open, in_progress, resolved, escalated
    resolution_plan: Optional[str] = None
    owner: Optional[str] = None
    target_resolution_date: Optional[date] = None
    actual_resolution_date: Optional[date] = None

class TeamCoaching(BaseModel):
    coaching_area: str  # agile_practices, collaboration, technical_skills
    current_maturity: str  # forming, storming, norming, performing
    improvement_goals: List[str]
    coaching_activities: List[str]
    success_indicators: List[str]
    timeline: str
    resources_needed: List[str]
    progress_tracking: str

class CeremonyFacilitation(BaseModel):
    ceremony_type: str  # sprint_planning, daily_standup, review, retrospective
    agenda: List[str]
    duration: int  # minutes
    participants: List[str]
    objectives: List[str]
    facilitation_techniques: List[str]
    expected_outcomes: List[str]
    preparation_checklist: List[str]

class AgileMetrics(BaseModel):
    team_velocity_trend: List[float]  # last 6 sprints
    sprint_goal_achievement_rate: float
    story_completion_rate: float
    defect_escape_rate: float
    cycle_time_average: float  # days
    lead_time_average: float  # days
    team_happiness_index: float
    process_adherence_score: float
    continuous_improvement_rate: float

class ConflictResolution(BaseModel):
    conflict_id: str
    conflict_type: str  # interpersonal, process, technical, resource
    parties_involved: List[str]
    description: str
    impact_assessment: str
    resolution_approach: str
    mediation_steps: List[str]
    agreed_solution: Optional[str] = None
    follow_up_actions: List[str]
    prevention_measures: List[str]

class SMAgent(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are an expert Scrum Master in the BMad Method framework.
        
        Your responsibilities include:
        - Facilitating Scrum ceremonies and agile practices
        - Removing impediments and blockers for the team
        - Coaching the team on agile principles and practices
        - Protecting the team from external distractions
        - Fostering collaboration and continuous improvement
        - Tracking and reporting on team metrics and progress
        - Resolving conflicts and improving team dynamics
        - Ensuring adherence to Scrum framework and BMad Method
        
        Always focus on servant leadership and team empowerment.
        Promote transparency, inspection, and adaptation.
        Foster a culture of continuous learning and improvement.
        """
        
        super().__init__(
            role="sm",
            system_prompt=system_prompt,
            response_model=SprintPlan
        )
    
    async def create_sprint_plan(self, product_backlog: List[str], team_capacity: int, sprint_duration: int) -> SprintPlan:
        """Create comprehensive sprint plan with goals and backlog items."""
        backlog_summary = "\n".join([f"- {item}" for item in product_backlog[:10]])  # Top 10 items
        
        prompt = f"""
        Create a comprehensive sprint plan based on the following inputs:
        
        Product Backlog (Top Items):
        {backlog_summary}
        
        Team Capacity: {team_capacity} story points
        Sprint Duration: {sprint_duration} days
        
        Create a sprint plan including:
        - Clear, achievable sprint goal with success criteria
        - Prioritized backlog items that fit within team capacity
        - Realistic story point estimates using Fibonacci sequence
        - Clear acceptance criteria and definition of done
        - Schedule for all Scrum ceremonies
        - Success metrics and tracking approach
        - Risk identification and mitigation
        
        Ensure the sprint goal is:
        - Specific and measurable
        - Aligned with product objectives
        - Achievable within the sprint timeframe
        - Valuable to stakeholders
        """
        
        return await self.execute_with_logging(prompt)
    
    async def facilitate_daily_standup(self, team_updates: List[Dict[str, str]], sprint_progress: Dict[str, Any]) -> DailyStandupReport:
        """Facilitate daily standup and generate report."""
        updates_summary = "\n".join([f"{member}: {update.get('today', 'No update')}" for member, update in enumerate(team_updates)])
        
        prompt = f"""
        Facilitate a daily standup meeting and generate a comprehensive report:
        
        Team Updates:
        {updates_summary}
        
        Sprint Progress:
        {sprint_progress}
        
        Generate standup report including:
        - Summary of team member updates and commitments
        - Identified blockers and impediments
        - Action items and follow-up tasks
        - Sprint progress assessment (on track, at risk, behind)
        - Burndown chart status and trends
        - Recommendations for team and stakeholders
        
        Focus on:
        - Identifying and addressing blockers quickly
        - Ensuring team collaboration and communication
        - Maintaining sprint goal focus
        - Promoting transparency and accountability
        """
        
        temp_agent = BMadAgent(
            role="sm-standup",
            system_prompt=self.system_prompt,
            response_model=DailyStandupReport
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def conduct_sprint_retrospective(self, sprint_data: Dict[str, Any], team_feedback: List[str]) -> SprintRetrospective:
        """Conduct sprint retrospective and identify improvements."""
        prompt = f"""
        Conduct a comprehensive sprint retrospective based on the following data:
        
        Sprint Data:
        {sprint_data}
        
        Team Feedback:
        {chr(10).join(team_feedback)}
        
        Facilitate retrospective covering:
        - What went well during the sprint
        - What could be improved or done differently
        - Specific, actionable improvement items
        - Team mood and satisfaction assessment
        - Velocity and performance analysis
        - Process improvements and experiments
        - Focus areas for the next sprint
        
        Ensure retrospective outcomes are:
        - Specific and actionable
        - Owned by team members
        - Measurable and time-bound
        - Focused on continuous improvement
        - Balanced between celebrating successes and addressing challenges
        """
        
        temp_agent = BMadAgent(
            role="sm-retrospective",
            system_prompt=self.system_prompt,
            response_model=SprintRetrospective
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def track_team_metrics(self, sprint_history: List[Dict[str, Any]], current_sprint: Dict[str, Any]) -> TeamMetrics:
        """Track and analyze team performance metrics."""
        history_summary = "\n".join([f"Sprint {i+1}: {sprint.get('velocity', 0)} points" for i, sprint in enumerate(sprint_history)])
        
        prompt = f"""
        Analyze team performance metrics based on sprint history and current progress:
        
        Sprint History:
        {history_summary}
        
        Current Sprint:
        {current_sprint}
        
        Calculate and analyze metrics including:
        - Team velocity trends and predictability
        - Burndown progress and completion patterns
        - Commitment accuracy and planning effectiveness
        - Quality metrics and defect rates
        - Team satisfaction and collaboration scores
        - Delivery predictability and consistency
        - Areas needing improvement or coaching
        
        Provide insights on:
        - Performance trends and patterns
        - Factors affecting team productivity
        - Recommendations for improvement
        - Coaching opportunities and focus areas
        """
        
        temp_agent = BMadAgent(
            role="sm-metrics",
            system_prompt=self.system_prompt,
            response_model=TeamMetrics
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def manage_impediments(self, current_impediments: List[str], team_context: Dict[str, Any]) -> List[ImpedimentLog]:
        """Manage and track team impediments."""
        impediments_text = "\n".join([f"- {impediment}" for impediment in current_impediments])
        
        prompt = f"""
        Manage and track team impediments based on current issues:
        
        Current Impediments:
        {impediments_text}
        
        Team Context:
        {team_context}
        
        For each impediment, create a comprehensive log including:
        - Clear description and impact assessment
        - Affected team members and work items
        - Priority level based on impact and urgency
        - Resolution plan and ownership assignment
        - Target resolution timeline
        - Escalation path if needed
        
        Focus on:
        - Quick resolution of blocking issues
        - Root cause analysis and prevention
        - Clear ownership and accountability
        - Regular follow-up and status updates
        - Learning opportunities from impediments
        """
        
        class ImpedimentLogList(BaseModel):
            impediments: List[ImpedimentLog]
        
        temp_agent = BMadAgent(
            role="sm-impediments",
            system_prompt=self.system_prompt,
            response_model=ImpedimentLogList
        )
        
        result = await temp_agent.execute_with_logging(prompt)
        return result.impediments
    
    async def coach_team(self, team_assessment: Dict[str, Any], improvement_goals: List[str]) -> TeamCoaching:
        """Provide team coaching and development guidance."""
        prompt = f"""
        Develop a team coaching plan based on assessment and improvement goals:
        
        Team Assessment:
        {team_assessment}
        
        Improvement Goals:
        {chr(10).join(improvement_goals)}
        
        Create coaching plan including:
        - Current team maturity and development stage
        - Specific improvement goals and objectives
        - Coaching activities and interventions
        - Success indicators and measurement approach
        - Timeline and milestone planning
        - Resources and support needed
        - Progress tracking and adjustment mechanisms
        
        Focus coaching on:
        - Agile mindset and practices adoption
        - Team collaboration and communication
        - Technical skills and capabilities
        - Self-organization and empowerment
        - Continuous learning and improvement
        """
        
        temp_agent = BMadAgent(
            role="sm-coaching",
            system_prompt=self.system_prompt,
            response_model=TeamCoaching
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def plan_ceremony(self, ceremony_type: str, team_context: Dict[str, Any], objectives: List[str]) -> CeremonyFacilitation:
        """Plan and prepare for Scrum ceremony facilitation."""
        prompt = f"""
        Plan facilitation for a {ceremony_type} ceremony:
        
        Team Context:
        {team_context}
        
        Ceremony Objectives:
        {chr(10).join(objectives)}
        
        Create facilitation plan including:
        - Detailed agenda with time allocations
        - Participant list and role assignments
        - Clear objectives and expected outcomes
        - Facilitation techniques and activities
        - Preparation checklist and materials needed
        - Success criteria and evaluation approach
        
        Tailor the ceremony to:
        - Team maturity and experience level
        - Current sprint context and challenges
        - Specific improvement opportunities
        - Time constraints and availability
        - Remote or hybrid participation needs
        """
        
        temp_agent = BMadAgent(
            role="sm-ceremony",
            system_prompt=self.system_prompt,
            response_model=CeremonyFacilitation
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def resolve_conflict(self, conflict_description: str, parties_involved: List[str], context: Dict[str, Any]) -> ConflictResolution:
        """Facilitate conflict resolution within the team."""
        prompt = f"""
        Facilitate resolution of a team conflict:
        
        Conflict Description:
        {conflict_description}
        
        Parties Involved:
        {', '.join(parties_involved)}
        
        Context:
        {context}
        
        Develop conflict resolution approach including:
        - Conflict type classification and root cause analysis
        - Impact assessment on team and project
        - Mediation approach and facilitation steps
        - Communication guidelines and ground rules
        - Solution options and compromise strategies
        - Agreement documentation and follow-up plan
        - Prevention measures for similar conflicts
        
        Focus on:
        - Understanding all perspectives and concerns
        - Finding win-win solutions when possible
        - Maintaining team relationships and trust
        - Learning opportunities and team growth
        - Sustainable resolution and prevention
        """
        
        temp_agent = BMadAgent(
            role="sm-conflict",
            system_prompt=self.system_prompt,
            response_model=ConflictResolution
        )
        
        return await temp_agent.execute_with_logging(prompt)
    
    async def analyze_agile_maturity(self, team_practices: Dict[str, Any], performance_data: Dict[str, Any]) -> AgileMetrics:
        """Analyze team's agile maturity and practices effectiveness."""
        prompt = f"""
        Analyze the team's agile maturity and practices effectiveness:
        
        Current Team Practices:
        {team_practices}
        
        Performance Data:
        {performance_data}
        
        Analyze and provide metrics on:
        - Velocity trends and predictability over time
        - Sprint goal achievement and success rates
        - Story completion rates and flow efficiency
        - Quality metrics and defect management
        - Cycle time and lead time optimization
        - Team happiness and engagement levels
        - Process adherence and framework compliance
        - Continuous improvement adoption and impact
        
        Provide insights on:
        - Agile maturity level and growth areas
        - Practice effectiveness and optimization opportunities
        - Team dynamics and collaboration quality
        - Recommendations for next-level improvements
        """
        
        temp_agent = BMadAgent(
            role="sm-maturity",
            system_prompt=self.system_prompt,
            response_model=AgileMetrics
        )
        
        return await temp_agent.execute_with_logging(prompt)