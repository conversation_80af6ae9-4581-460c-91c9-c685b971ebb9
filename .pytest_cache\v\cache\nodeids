["tests/test_agents.py::TestAgentIntegration::test_agent_roles_unique", "tests/test_agents.py::TestAgentIntegration::test_all_agents_importable", "tests/test_agents.py::TestAnalystAgent::test_agent_initialization", "tests/test_agents.py::TestAnalystAgent::test_analyze_requirements_structure", "tests/test_agents.py::TestAnalystAgent::test_create_user_stories_structure", "tests/test_agents.py::TestArchitectAgent::test_agent_initialization", "tests/test_agents.py::TestArchitectAgent::test_design_architecture_structure", "tests/test_agents.py::TestBMadOrchestrator::test_agent_initialization", "tests/test_agents.py::TestBMadOrchestrator::test_agents_property", "tests/test_agents.py::TestBMadOrchestrator::test_get_agent_status", "tests/test_agents.py::TestBMadOrchestrator::test_recommend_workflow_structure", "tests/test_agents.py::TestBMadOrchestrator::test_route_request_structure", "tests/test_agents.py::TestBMadOrchestrator::test_select_agent_structure", "tests/test_agents.py::TestDevOpsAgent::test_agent_initialization", "tests/test_agents.py::TestDeveloperAgent::test_agent_initialization", "tests/test_agents.py::TestPMAgent::test_agent_initialization", "tests/test_agents.py::TestPMAgent::test_create_project_plan_structure", "tests/test_agents.py::TestPOAgent::test_agent_initialization", "tests/test_agents.py::TestQAAgent::test_agent_initialization", "tests/test_agents.py::TestSMAgent::test_agent_initialization", "tests/test_agents.py::TestUXAgent::test_agent_initialization", "tests/test_workflows.py::TestBaseWorkflow::test_execute_step", "tests/test_workflows.py::TestBaseWorkflow::test_get_next_step", "tests/test_workflows.py::TestBaseWorkflow::test_get_step", "tests/test_workflows.py::TestBaseWorkflow::test_start_workflow", "tests/test_workflows.py::TestBrownfieldWorkflow::test_define_steps", "tests/test_workflows.py::TestBrownfieldWorkflow::test_execute_scope_classification", "tests/test_workflows.py::TestBrownfieldWorkflow::test_workflow_initialization", "tests/test_workflows.py::TestGreenfieldWorkflow::test_define_steps", "tests/test_workflows.py::TestGreenfieldWorkflow::test_execute_requirements_gathering", "tests/test_workflows.py::TestGreenfieldWorkflow::test_workflow_initialization", "tests/test_workflows.py::TestWorkflowFactory::test_create_brownfield_workflow", "tests/test_workflows.py::TestWorkflowFactory::test_create_greenfield_workflow", "tests/test_workflows.py::TestWorkflowFactory::test_create_unknown_workflow", "tests/test_workflows.py::TestWorkflowFactory::test_get_available_workflows", "tests/test_workflows.py::TestWorkflowFactory::test_get_workflow_info", "tests/test_workflows.py::TestWorkflowFactory::test_is_workflow_available"]