import asyncio
import logging
from typing import Any, Callable, Optional
from functools import wraps
from .config import config

class BMadError(Exception):
    """Base exception for BMad agents."""
    pass

class AgentTimeoutError(BMadError):
    """Raised when agent execution times out."""
    pass

class AgentExecutionError(BMadError):
    """Raised when agent execution fails."""
    pass

class WorkflowError(BMadError):
    """Raised when workflow execution fails."""
    pass

def with_retry(max_retries: int = None, backoff_factor: float = 1.0):
    """Decorator to add retry logic to agent methods."""
    max_retries = max_retries or config.max_retries
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = backoff_factor * (2 ** attempt)
                        logging.getLogger('bmad').warning(
                            f"Attempt {attempt + 1} failed, retrying in {wait_time}s: {str(e)}"
                        )
                        await asyncio.sleep(wait_time)
                    else:
                        logging.getLogger('bmad').error(
                            f"All {max_retries + 1} attempts failed: {str(e)}"
                        )
            
            raise AgentExecutionError(f"Failed after {max_retries + 1} attempts") from last_exception
        
        return wrapper
    return decorator

def with_timeout(timeout: int = None):
    """Decorator to add timeout to agent methods."""
    timeout = timeout or config.agent_timeout
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout)
            except asyncio.TimeoutError:
                raise AgentTimeoutError(f"Agent execution timed out after {timeout}s")
        
        return wrapper
    return decorator

class CircuitBreaker:
    """Circuit breaker pattern for API calls."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half-open
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        if self.state == "open":
            if self._should_attempt_reset():
                self.state = "half-open"
            else:
                raise AgentExecutionError("Circuit breaker is open")
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise
    
    def _should_attempt_reset(self) -> bool:
        return (
            self.last_failure_time and
            (asyncio.get_event_loop().time() - self.last_failure_time) > self.recovery_timeout
        )
    
    def _on_success(self):
        self.failure_count = 0
        self.state = "closed"
    
    def _on_failure(self):
        self.failure_count += 1
        self.last_failure_time = asyncio.get_event_loop().time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"