import pytest
import pytest_asyncio
import asyncio
from unittest.mock import Mock, patch
from bmad_agents.agents.analyst import AnalystAgent, RequirementAnalysis, UserStory
from bmad_agents.agents.architect import ArchitectAgent, SystemArchitecture, TechnicalSpecification
from bmad_agents.agents.pm import PMAgent, ProjectPlan, ProjectStatus
from bmad_agents.agents.po import POAgent, ProductBacklog, ProductVision
from bmad_agents.agents.developer import DeveloperAgent, CodeGeneration, ImplementationPlan
from bmad_agents.agents.qa import QAAgent, TestPlan, QualityReport
from bmad_agents.agents.devops import DevOpsAgent, DeploymentPlan, InfrastructureComponent
from bmad_agents.agents.sm import SMAgent, SprintPlan, DailyStandupReport
from bmad_agents.agents.ux import UXAgent, UserPersona, UserJourney
from bmad_agents.agents.orchestrator import BMadOrchestrator, OrchestrationResponse, AgentSelection, WorkflowRecommendation

class TestAnalystAgent:
    @pytest.fixture
    def analyst_agent(self):
        return AnalystAgent()
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, analyst_agent):
        """Test that AnalystAgent initializes correctly."""
        assert analyst_agent.role == "analyst"
        assert hasattr(analyst_agent, 'analyze_requirements')
        assert hasattr(analyst_agent, 'create_user_stories')
    
    @pytest.mark.asyncio
    async def test_analyze_requirements_structure(self, analyst_agent):
        """Test that analyze_requirements returns proper structure."""
        # Mock the AI response to avoid actual API calls in tests
        mock_response = RequirementAnalysis(
            summary="Test requirement analysis",
            functional_requirements=["User login", "Data storage"],
            non_functional_requirements=["Performance", "Security"],
            assumptions=["Users have internet access"],
            risks=["API rate limits"],
            recommendations=["Use caching"]
        )
        
        with patch.object(analyst_agent, 'run', return_value=mock_response):
            requirements = "Create a web application for task management"
            result = await analyst_agent.analyze_requirements(requirements)
            
            assert isinstance(result, RequirementAnalysis)
            assert result.summary == "Test requirement analysis"
            assert len(result.functional_requirements) == 2
            assert len(result.non_functional_requirements) == 2
    
    @pytest.mark.asyncio
    async def test_create_user_stories_structure(self, analyst_agent):
        """Test that create_user_stories returns proper structure."""
        mock_stories = [
            UserStory(
                title="User Login",
                description="As a user, I want to login",
                acceptance_criteria=["Valid credentials accepted"],
                priority="High",
                effort_estimate="Medium"
            )
        ]
        
        with patch.object(analyst_agent, 'create_user_stories', return_value=mock_stories):
            requirements = "User authentication system"
            result = await analyst_agent.create_user_stories(requirements)
            
            assert isinstance(result, list)
            assert len(result) == 1
            assert isinstance(result[0], UserStory)
            assert result[0].title == "User Login"

class TestArchitectAgent:
    @pytest.fixture
    def architect_agent(self):
        return ArchitectAgent()
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, architect_agent):
        """Test that ArchitectAgent initializes correctly."""
        assert architect_agent.role == "architect"
        assert hasattr(architect_agent, 'design_architecture')
        assert hasattr(architect_agent, 'create_technical_specs')
    
    @pytest.mark.asyncio
    async def test_design_architecture_structure(self, architect_agent):
        """Test that design_architecture returns proper structure."""
        from bmad_agents.agents.architect import TechnicalComponent, ArchitecturalDecision
        
        mock_response = SystemArchitecture(
            overview="Microservices architecture",
            components=[
                TechnicalComponent(
                    name="API Gateway",
                    description="Routes requests",
                    technology="Node.js",
                    responsibilities=["Request routing"],
                    interfaces=["REST API"],
                    dependencies=[]
                )
            ],
            architectural_decisions=[
                ArchitecturalDecision(
                    decision="Use microservices",
                    rationale="Better scalability",
                    alternatives_considered=["Monolith"],
                    implications=["Distributed complexity"]
                )
            ],
            technology_stack={"backend": "Node.js", "database": "PostgreSQL"},
            deployment_considerations=["Container-based"],
            scalability_considerations=["Horizontal scaling"],
            security_considerations=["JWT authentication"]
        )
        
        with patch.object(architect_agent, 'run', return_value=mock_response):
            requirements = "Design architecture for a scalable web application"
            result = await architect_agent.design_architecture(requirements)
            
            assert isinstance(result, SystemArchitecture)
            assert result.overview == "Microservices architecture"
            assert len(result.components) == 1

class TestPMAgent:
    @pytest.fixture
    def pm_agent(self):
        return PMAgent()
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, pm_agent):
        """Test that PMAgent initializes correctly."""
        assert pm_agent.role == "pm"
        assert hasattr(pm_agent, 'create_project_plan')
        assert hasattr(pm_agent, 'track_progress')
    
    @pytest.mark.asyncio
    async def test_create_project_plan_structure(self, pm_agent):
        """Test that create_project_plan returns proper structure."""
        from bmad_agents.agents.pm import ProjectTask, ProjectMilestone, ResourceAllocation, RiskAssessment
        from datetime import date
        
        mock_response = ProjectPlan(
            project_name="Test Project",
            description="A test project",
            objectives=["Deliver on time"],
            scope="Feature A and Feature B",
            tasks=[
                ProjectTask(
                    task_id="T001",
                    name="Setup",
                    description="Initial setup",
                    estimated_hours=8.0
                )
            ],
            milestones=[
                ProjectMilestone(
                    milestone_id="M001",
                    name="MVP",
                    description="Minimum viable product",
                    target_date=date(2024, 3, 1),
                    deliverables=["Core features"],
                    success_criteria=["All tests pass"]
                )
            ],
            resources=[
                ResourceAllocation(
                    resource_name="Developer",
                    role="Developer",
                    availability_percentage=100.0,
                    skills=["Python"]
                )
            ],
            risks=[
                RiskAssessment(
                    risk_id="R001",
                    description="Technical complexity",
                    probability="medium",
                    impact="high",
                    mitigation_strategy="Prototype early"
                )
            ],
            timeline_weeks=12,
            success_metrics=["On time delivery"]
        )
        
        with patch.object(pm_agent, 'run', return_value=mock_response):
            requirements = "Create project plan for web application"
            result = await pm_agent.create_project_plan(requirements, 90)
            
            assert isinstance(result, ProjectPlan)
            assert result.project_name == "Test Project"
            assert len(result.milestones) == 1

class TestPOAgent:
    @pytest.fixture
    def po_agent(self):
        return POAgent()
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, po_agent):
        """Test that POAgent initializes correctly."""
        assert po_agent.role == "po"
        assert hasattr(po_agent, 'define_product_vision')
        assert hasattr(po_agent, 'create_product_backlog')

class TestDeveloperAgent:
    @pytest.fixture
    def developer_agent(self):
        return DeveloperAgent()
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, developer_agent):
        """Test that DeveloperAgent initializes correctly."""
        assert developer_agent.role == "developer"
        assert hasattr(developer_agent, 'generate_code')
        assert hasattr(developer_agent, 'create_implementation_plan')

class TestQAAgent:
    @pytest.fixture
    def qa_agent(self):
        return QAAgent()
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, qa_agent):
        """Test that QAAgent initializes correctly."""
        assert qa_agent.role == "qa"
        assert hasattr(qa_agent, 'create_test_plan')
        assert hasattr(qa_agent, 'generate_quality_report')

class TestDevOpsAgent:
    @pytest.fixture
    def devops_agent(self):
        return DevOpsAgent()
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, devops_agent):
        """Test that DevOpsAgent initializes correctly."""
        assert devops_agent.role == "devops"
        assert hasattr(devops_agent, 'design_infrastructure')
        assert hasattr(devops_agent, 'create_deployment_plan')

class TestSMAgent:
    @pytest.fixture
    def sm_agent(self):
        return SMAgent()
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, sm_agent):
        """Test that SMAgent initializes correctly."""
        assert sm_agent.role == "sm"
        assert hasattr(sm_agent, 'create_sprint_plan')
        assert hasattr(sm_agent, 'facilitate_daily_standup')

class TestUXAgent:
    @pytest.fixture
    def ux_agent(self):
        return UXAgent()
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, ux_agent):
        """Test that UXAgent initializes correctly."""
        assert ux_agent.role == "ux"
        assert hasattr(ux_agent, 'create_user_personas')
        assert hasattr(ux_agent, 'design_user_journey')

class TestBMadOrchestrator:
    @pytest.fixture
    def orchestrator(self):
        return BMadOrchestrator()
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, orchestrator):
        """Test that BMadOrchestrator initializes correctly."""
        assert orchestrator.role == "orchestrator"
        assert hasattr(orchestrator, 'route_request')
        assert hasattr(orchestrator, 'select_agent')
        assert hasattr(orchestrator, 'recommend_workflow')
        assert hasattr(orchestrator, 'execute_agent_request')
        assert hasattr(orchestrator, 'get_help')
        assert hasattr(orchestrator, 'get_agent_status')
        assert hasattr(orchestrator, 'coordinate_handoff')
    
    @pytest.mark.asyncio
    async def test_route_request_structure(self, orchestrator):
        """Test that route_request returns proper structure."""
        mock_response = OrchestrationResponse(
            action="route_to_agent",
            target_agent="analyst",
            message="Routing to analyst for requirements analysis",
            next_steps=["Analyze requirements", "Create user stories"]
        )
        
        with patch.object(orchestrator, 'run', return_value=mock_response):
            request = "I need help analyzing requirements for a new project"
            result = await orchestrator.route_request(request)
            
            assert isinstance(result, OrchestrationResponse)
            assert result.action == "route_to_agent"
            assert result.target_agent == "analyst"
            assert len(result.next_steps) == 2
    
    @pytest.mark.asyncio
    async def test_select_agent_structure(self, orchestrator):
        """Test that select_agent returns proper structure."""
        mock_response = AgentSelection(
            selected_agent="architect",
            rationale="This request involves system design decisions",
            confidence=0.9,
            alternative_agents=["developer", "devops"]
        )
        
        with patch('bmad_agents.base.bmad_agent.BMadAgent.run', return_value=mock_response):
            request = "Design a microservices architecture"
            result = await orchestrator.select_agent(request)
            
            assert isinstance(result, AgentSelection)
            assert result.selected_agent == "architect"
            assert result.confidence == 0.9
            assert len(result.alternative_agents) == 2
    
    @pytest.mark.asyncio
    async def test_recommend_workflow_structure(self, orchestrator):
        """Test that recommend_workflow returns proper structure."""
        mock_response = WorkflowRecommendation(
            recommended_workflow="greenfield-fullstack",
            rationale="This is a new full-stack application",
            required_inputs=["Requirements", "Technology preferences"],
            expected_outputs=["Architecture", "Implementation plan"],
            estimated_duration="2-4 weeks"
        )
        
        with patch('bmad_agents.base.bmad_agent.BMadAgent.run', return_value=mock_response):
            request = "Create a new web application from scratch"
            result = await orchestrator.recommend_workflow(request)
            
            assert isinstance(result, WorkflowRecommendation)
            assert result.recommended_workflow == "greenfield-fullstack"
            assert len(result.required_inputs) == 2
            assert len(result.expected_outputs) == 2
    
    @pytest.mark.asyncio
    async def test_get_agent_status(self, orchestrator):
        """Test that get_agent_status returns proper structure."""
        status = await orchestrator.get_agent_status()
        
        assert isinstance(status, dict)
        assert "available_agents" in status
        assert "active_workflows" in status
        assert "orchestrator_role" in status
        assert "created_at" in status
        assert len(status["available_agents"]) == 9  # All agent types
    
    def test_agents_property(self, orchestrator):
        """Test that agents property returns all available agents."""
        agents = orchestrator.agents
        expected_agents = ['analyst', 'architect', 'pm', 'po', 'sm', 'developer', 'qa', 'ux', 'devops']
        
        assert isinstance(agents, dict)
        assert set(agents.keys()) == set(expected_agents)
        
        # Test that each agent is properly instantiated
        for agent_name, agent in agents.items():
            assert hasattr(agent, 'role')
            assert agent.role == agent_name

# Integration tests
class TestAgentIntegration:
    """Test that all agents can be imported and initialized together."""
    
    def test_all_agents_importable(self):
        """Test that all agents can be imported without errors."""
        from bmad_agents.agents import (
            AnalystAgent, ArchitectAgent, PMAgent, POAgent,
            DeveloperAgent, QAAgent, DevOpsAgent, SMAgent, UXAgent, BMadOrchestrator
        )
        
        # Test that all agents can be instantiated
        agents = [
            AnalystAgent(),
            ArchitectAgent(),
            PMAgent(),
            POAgent(),
            DeveloperAgent(),
            QAAgent(),
            DevOpsAgent(),
            SMAgent(),
            UXAgent(),
            BMadOrchestrator()
        ]
        
        assert len(agents) == 10
        
        # Test that all agents have required attributes
        for agent in agents:
            assert hasattr(agent, 'role')
            assert hasattr(agent, 'system_prompt')
            assert hasattr(agent, 'logger')
            assert hasattr(agent, 'created_at')
    
    def test_agent_roles_unique(self):
        """Test that all agents have unique roles."""
        from bmad_agents.agents import (
            AnalystAgent, ArchitectAgent, PMAgent, POAgent,
            DeveloperAgent, QAAgent, DevOpsAgent, SMAgent, UXAgent, BMadOrchestrator
        )
        
        agents = [
            AnalystAgent(),
            ArchitectAgent(),
            PMAgent(),
            POAgent(),
            DeveloperAgent(),
            QAAgent(),
            DevOpsAgent(),
            SMAgent(),
            UXAgent(),
            BMadOrchestrator()
        ]
        
        roles = [agent.role for agent in agents]
        assert len(roles) == len(set(roles))  # All roles should be unique
        
        expected_roles = {
            "analyst", "architect", "pm", "po", 
            "developer", "qa", "devops", "sm", "ux", "orchestrator"
        }
        assert set(roles) == expected_roles