# BMad Pydantic AI Agents - Technical Architecture

## Architecture Overview

The BMad Pydantic AI Agents system extends the existing Pydantic AI framework to implement the complete BMad Method with specialized agents, workflow orchestration, and multi-agent coordination capabilities.

### High-Level Architecture

```mermaid
graph TB
    subgraph "User Interface Layer"
        UI[User Interface]
        CLI[Command Line Interface]
        API[REST API]
    end
    
    subgraph "Orchestration Layer"
        ORCH[BMad Orchestrator]
        WE[Workflow Engine]
        SM[State Manager]
    end
    
    subgraph "Agent Layer"
        AN[Analyst Agent]
        AR[Architect Agent]
        PM[PM Agent]
        PO[PO Agent]
        SCM[SM Agent]
        DEV[Dev Agent]
        QA[QA Agent]
        UX[UX Expert Agent]
    end
    
    subgraph "Core Infrastructure"
        BA[Base BMad Agent]
        CM[Communication Manager]
        LM[Logging Manager]
        CFG[Configuration Manager]
    end
    
    subgraph "External Services"
        GEMINI[Google Gemini 2.5 Flash]
        OPENAI[OpenAI GPT-4o-mini]
        OTHER[Other AI Models]
    end
    
    UI --> ORCH
    CLI --> ORCH
    API --> ORCH
    
    ORCH --> WE
    ORCH --> SM
    WE --> AN
    WE --> AR
    WE --> PM
    WE --> PO
    WE --> SCM
    WE --> DEV
    WE --> QA
    WE --> UX
    
    AN --> BA
    AR --> BA
    PM --> BA
    PO --> BA
    SCM --> BA
    DEV --> BA
    QA --> BA
    UX --> BA
    
    BA --> CM
    BA --> LM
    BA --> CFG
    
    BA --> GEMINI
    BA --> OPENAI
    BA --> OTHER
```

## Component Architecture

### 1. Base Infrastructure Layer

#### BMadAgent Base Class
```python
class BMadAgent(Agent):
    """
    Base class for all BMad Method agents.
    Extends Pydantic AI Agent with BMad-specific capabilities.
    """
    def __init__(
        self,
        role: str,
        model: str = 'gemini-2.0-flash-exp',
        system_prompt: str = None,
        response_model: Type[BaseModel] = None,
        tools: List[Tool] = None
    )
```

**Key Features:**
- Role-based initialization
- Standardized communication protocols
- Built-in logging and monitoring
- Error handling and recovery
- State management integration

#### Communication Manager
```python
class CommunicationManager:
    """
    Manages inter-agent communication and message routing.
    """
    async def send_message(self, from_agent: str, to_agent: str, message: AgentMessage)
    async def broadcast_message(self, from_agent: str, message: AgentMessage)
    async def get_agent_response(self, agent_id: str, request: AgentRequest)
```

#### State Manager
```python
class StateManager:
    """
    Manages workflow state and agent context.
    """
    async def save_state(self, workflow_id: str, state: WorkflowState)
    async def load_state(self, workflow_id: str) -> WorkflowState
    async def update_agent_context(self, agent_id: str, context: AgentContext)
```

### 2. Agent Layer Architecture

#### Specialized Agent Implementation
Each BMad agent inherits from BMadAgent and implements role-specific capabilities:

```python
class AnalystAgent(BMadAgent):
    def __init__(self):
        super().__init__(
            role="analyst",
            system_prompt=ANALYST_SYSTEM_PROMPT,
            response_model=AnalysisResponse,
            tools=[analysis_tools]
        )
    
    async def analyze_requirements(self, requirements: str) -> AnalysisResponse
    async def create_user_stories(self, analysis: AnalysisResponse) -> List[UserStory]
```

#### Agent Specializations

| Agent | Primary Responsibilities | Key Methods |
|-------|-------------------------|-------------|
| **Analyst** | Requirements analysis, user story creation | `analyze_requirements()`, `create_user_stories()` |
| **Architect** | System design, technical decisions | `design_architecture()`, `create_technical_specs()` |
| **PM** | Project planning, resource management | `create_project_plan()`, `track_progress()` |
| **PO** | Product vision, backlog management | `define_product_vision()`, `prioritize_backlog()` |
| **SM** | Process facilitation, team coordination | `facilitate_workflow()`, `resolve_blockers()` |
| **Dev** | Code implementation, technical execution | `implement_feature()`, `code_review()` |
| **QA** | Quality assurance, testing | `create_test_plan()`, `execute_tests()` |
| **UX Expert** | User experience design, usability | `design_user_interface()`, `conduct_usability_review()` |

### 3. Orchestration Layer Architecture

#### BMad Orchestrator
```python
class BMadOrchestrator(BMadAgent):
    """
    Central coordination agent that manages workflow execution
    and agent interactions.
    """
    def __init__(self):
        self.agents = self._initialize_agents()
        self.workflow_engine = WorkflowEngine()
        self.state_manager = StateManager()
    
    async def execute_workflow(self, workflow_type: str, context: dict)
    async def route_request(self, request: str) -> AgentResponse
    async def coordinate_agents(self, workflow_step: WorkflowStep)
```

#### Workflow Engine
```python
class WorkflowEngine:
    """
    Executes BMad Method workflows with multi-agent coordination.
    """
    async def execute_brownfield_workflow(self, context: BrownfieldContext)
    async def execute_greenfield_workflow(self, context: GreenfieldContext)
    async def handle_workflow_step(self, step: WorkflowStep, context: WorkflowContext)
```

### 4. Data Models Architecture

#### Core Communication Models
```python
class AgentMessage(BaseModel):
    from_agent: str
    to_agent: str
    message_type: MessageType
    content: dict
    timestamp: datetime
    correlation_id: str

class AgentRequest(BaseModel):
    request_id: str
    agent_role: str
    action: str
    parameters: dict
    context: dict

class AgentResponse(BaseModel):
    request_id: str
    agent_role: str
    status: ResponseStatus
    result: dict
    metadata: dict
```

#### Workflow State Models
```python
class WorkflowState(BaseModel):
    workflow_id: str
    workflow_type: str
    current_step: str
    completed_steps: List[str]
    agent_contexts: Dict[str, AgentContext]
    shared_context: dict
    created_at: datetime
    updated_at: datetime

class WorkflowStep(BaseModel):
    step_id: str
    step_name: str
    required_agents: List[str]
    input_requirements: dict
    output_specifications: dict
    dependencies: List[str]
```

## Integration Architecture

### Backward Compatibility Strategy

```python
# Existing functionality remains unchanged
from pydantic_ai import Agent
from example_agent import weather_agent  # Still works

# New BMad functionality is additive
from bmad_agents import BMadOrchestrator, AnalystAgent

# Both can coexist
weather_result = await weather_agent.run("What's the weather?")
bmad_result = await BMadOrchestrator().route_request("Analyze this project")
```

### Configuration Architecture

```python
# .env extensions (backward compatible)
GOOGLE_AI_API_KEY=existing_key
OPENAI_API_KEY=existing_key

# New BMad-specific configurations
BMAD_DEFAULT_MODEL=gemini-2.0-flash-exp
BMAD_WORKFLOW_PERSISTENCE=true
BMAD_AGENT_TIMEOUT=30
BMAD_MAX_CONCURRENT_AGENTS=5
```

### File Structure Integration

```
project_root/
├── existing_files/           # Unchanged
│   ├── example_agent.py
│   ├── test_pydantic_ai.py
│   └── requirements.txt
├── bmad_agents/              # New BMad implementation
│   ├── __init__.py
│   ├── base/
│   │   ├── bmad_agent.py
│   │   ├── communication.py
│   │   ├── state_manager.py
│   │   └── models.py
│   ├── agents/
│   │   ├── analyst.py
│   │   ├── architect.py
│   │   ├── pm.py
│   │   ├── po.py
│   │   ├── sm.py
│   │   ├── dev.py
│   │   ├── qa.py
│   │   ├── ux_expert.py
│   │   └── orchestrator.py
│   ├── workflows/
│   │   ├── base_workflow.py
│   │   ├── brownfield_fullstack.py
│   │   └── greenfield_fullstack.py
│   └── examples/
│       ├── single_agent_demo.py
│       ├── workflow_demo.py
│       └── orchestrator_demo.py
└── docs/                     # Enhanced documentation
    ├── prd.md               # This PRD
    ├── architecture.md      # This document
    └── agents/              # Agent-specific docs
```

## Performance Architecture

### Concurrency Strategy
- **Async/Await**: All agent interactions use async patterns
- **Connection Pooling**: Shared HTTP connections for AI model APIs
- **Rate Limiting**: Built-in rate limiting to prevent API quota exhaustion
- **Circuit Breaker**: Automatic fallback when services are unavailable

### Memory Management
- **Lazy Loading**: Agents instantiated only when needed
- **Context Cleanup**: Automatic cleanup of completed workflow contexts
- **State Persistence**: Workflow state persisted to disk to prevent memory bloat
- **Resource Limits**: Configurable limits on concurrent agents and memory usage

### Scalability Considerations
- **Horizontal Scaling**: Architecture supports multiple orchestrator instances
- **Load Balancing**: Request routing can be distributed across agent instances
- **State Synchronization**: Shared state management for multi-instance deployments
- **Monitoring Integration**: Built-in metrics for performance monitoring

## Security Architecture

### API Key Management
- **Environment Variables**: Secure storage of API keys in .env files
- **Key Rotation**: Support for API key rotation without system restart
- **Access Control**: Role-based access to different AI models
- **Audit Logging**: Complete audit trail of API usage

### Data Protection
- **Input Sanitization**: All user inputs sanitized before processing
- **Output Filtering**: AI responses filtered for sensitive information
- **Encryption**: Workflow state encrypted at rest
- **Privacy Controls**: User data handling compliance

## Monitoring and Observability

### Logging Architecture
```python
class BMadLogger:
    def log_agent_interaction(self, agent: str, action: str, context: dict)
    def log_workflow_step(self, workflow_id: str, step: str, status: str)
    def log_performance_metrics(self, metrics: PerformanceMetrics)
    def log_error(self, error: Exception, context: dict)
```

### Metrics Collection
- **Agent Performance**: Response times, success rates, error rates
- **Workflow Metrics**: Completion times, step success rates, bottlenecks
- **Resource Usage**: Memory consumption, API usage, concurrent operations
- **User Metrics**: Workflow usage patterns, agent preferences

## Deployment Architecture

### Development Environment
- **Local Development**: Full functionality in local virtual environment
- **Hot Reloading**: Agent code changes reflected without restart
- **Debug Mode**: Enhanced logging and error reporting
- **Testing Integration**: Comprehensive test suite integration

### Production Environment
- **Container Support**: Docker containerization for consistent deployment
- **Environment Configuration**: Production-specific configuration management
- **Health Checks**: Built-in health monitoring and reporting
- **Graceful Shutdown**: Proper cleanup of running workflows on shutdown

This architecture provides a robust, scalable, and maintainable foundation for implementing the BMad Method using Pydantic AI while ensuring full backward compatibility with existing functionality.