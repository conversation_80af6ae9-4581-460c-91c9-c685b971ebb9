from pydantic_ai import Agent
from pydantic import BaseModel
from typing import Type, List, Optional, Dict, Any
from datetime import datetime
import logging
import asyncio

class BMadAgent(Agent):
    """
    Base class for all BMad Method agents.
    Extends Pydantic AI Agent with BMad-specific capabilities.
    """
    
    def __init__(
        self,
        role: str,
        model: str = 'gemini-2.5-flash',
        system_prompt: str = None,
        response_model: Type[BaseModel] = None,
        tools: List = None,
        **kwargs
    ):
        self.role = role
        self.model = model  # Store model for later access
        self.created_at = datetime.now()
        self.logger = logging.getLogger(f"bmad.{role}")
        
        super().__init__(
            model=model,
            system_prompt=system_prompt or self._get_default_system_prompt(),
            result_type=response_model,
            tools=tools or [],  # Ensure tools is not None
            **kwargs
        )
    
    def _get_default_system_prompt(self) -> str:
        """
        Get the default system prompt for this agent role.
        Should be overridden by specific agent implementations.
        """
        return f"You are a {self.role} agent in the BMad Method framework. You specialize in {self.role.lower()} tasks and collaborate with other agents to deliver high-quality software solutions."
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request with BMad-specific logging and error handling.
        """
        self.logger.info(f"Processing request: {request.get('type', 'unknown')}")
        
        try:
            # Run the agent with the request
            result = await self.run(request.get('message', ''))
            
            response = {
                'agent': self.role,
                'timestamp': datetime.now().isoformat(),
                'status': 'success',
                'result': result.data if hasattr(result, 'data') else result,
                'request_id': request.get('id')
            }
            
            self.logger.info(f"Request processed successfully")
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing request: {str(e)}")
            return {
                'agent': self.role,
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e),
                'request_id': request.get('id')
            }
    
    def get_agent_info(self) -> Dict[str, Any]:
        """
        Get information about this agent.
        """
        return {
            'role': self.role,
            'model': self.model,
            'created_at': self.created_at.isoformat(),
            'capabilities': self._get_capabilities()
        }
    
    def _get_capabilities(self) -> List[str]:
        """
        Get the capabilities of this agent.
        Should be overridden by specific agent implementations.
        """
        return [f"{self.role.lower()}_tasks"]